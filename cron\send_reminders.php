<?php
/**
 * Enhanced Booking Reminder Cron Job
 * This script should be run every 5-10 minutes via cron job for robust reminder processing
 *
 * Add this to your crontab:
 * */5 * * * * /usr/bin/php /path/to/your/project/cron/send_reminders.php
 */

// Set the working directory to the project root
chdir(dirname(__DIR__));

require_once 'config/app.php';

// Prevent running from web browser for security
if (isset($_SERVER['HTTP_HOST'])) {
    die('This script can only be run from command line.');
}

// Log start of cron job
error_log("Enhanced booking reminder cron job started at " . date('Y-m-d H:i:s'));

try {
    $totalResults = [
        'processed' => 0,
        'sent' => 0,
        'failed' => 0,
        'skipped' => 0,
        'missed_recovered' => 0
    ];

    // 1. Process pending reminders
    echo "Processing pending reminders...\n";
    $pendingResults = processPendingReminders();
    $totalResults['processed'] += $pendingResults['processed'];
    $totalResults['sent'] += $pendingResults['sent'];
    $totalResults['failed'] += $pendingResults['failed'];
    $totalResults['skipped'] += $pendingResults['skipped'];

    // 2. Check for missed reminders and recover them
    echo "Checking for missed reminders...\n";
    $missedResults = checkMissedReminders();
    $totalResults['missed_recovered'] = $missedResults['sent'];

    // 3. Update expired bookings
    echo "Updating expired bookings...\n";
    $expiredCount = updateExpiredBookings();

    // 4. Clean up old data
    echo "Cleaning up old data...\n";
    cleanupEmailLogs();
    cleanupOldReminderLogs();

    // Log comprehensive results
    error_log("Reminder processing completed: " . json_encode($totalResults));
    error_log("Expired bookings updated: $expiredCount");

    // Output results
    echo "Cron job completed successfully\n";
    echo "Pending reminders processed: {$pendingResults['processed']}\n";
    echo "Reminders sent: {$pendingResults['sent']}\n";
    echo "Reminders failed: {$pendingResults['failed']}\n";
    echo "Reminders skipped: {$pendingResults['skipped']}\n";
    echo "Missed reminders recovered: {$missedResults['sent']}\n";
    echo "Expired bookings updated: $expiredCount\n";

    // Get reminder statistics
    $stats = getReminderStats(1); // Last 24 hours
    echo "24h Reminder Stats - Total: {$stats['total_reminders']}, Sent: {$stats['sent_reminders']}, Failed: {$stats['failed_reminders']}\n";

} catch (Exception $e) {
    error_log("Enhanced booking reminder cron job failed: " . $e->getMessage());
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}

/**
 * Update expired bookings
 */
function updateExpiredBookings() {
    global $database;
    
    // Update bookings that have passed their appointment time and are still pending/confirmed
    $result = $database->query(
        "UPDATE bookings 
         SET status = 'EXPIRED', updated_at = NOW() 
         WHERE status IN ('PENDING', 'CONFIRMED') 
         AND CONCAT(date, ' ', end_time) < NOW()"
    );
    
    return $database->rowCount();
}

/**
 * Clean up old email logs
 */
function cleanupEmailLogs() {
    global $database;

    try {
        $database->query(
            "DELETE FROM email_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY)"
        );

        $deletedCount = $database->rowCount();
        if ($deletedCount > 0) {
            error_log("Cleaned up $deletedCount old email log entries");
        }

        return $deletedCount;
    } catch (Exception $e) {
        error_log("Failed to cleanup email logs: " . $e->getMessage());
        return 0;
    }
}

/**
 * Clean up old reminder logs
 */
function cleanupOldReminderLogs() {
    global $database;

    try {
        // Clean up reminder logs older than 90 days
        $database->query(
            "DELETE FROM reminder_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY)"
        );

        $deletedCount = $database->rowCount();
        if ($deletedCount > 0) {
            error_log("Cleaned up $deletedCount old reminder log entries");
        }

        // Clean up old completed/skipped reminders (keep for 30 days)
        $database->query(
            "DELETE FROM booking_reminders
             WHERE status IN ('SENT', 'SKIPPED')
             AND updated_at < DATE_SUB(NOW(), INTERVAL 30 DAY)"
        );

        $deletedReminders = $database->rowCount();
        if ($deletedReminders > 0) {
            error_log("Cleaned up $deletedReminders old reminder entries");
        }

        return $deletedCount + $deletedReminders;
    } catch (Exception $e) {
        error_log("Failed to cleanup reminder logs: " . $e->getMessage());
        return 0;
    }
}
?>
