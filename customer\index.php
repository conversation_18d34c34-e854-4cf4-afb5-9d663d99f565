<?php
require_once __DIR__ . '/../config/app.php';
require_once __DIR__ . '/../includes/customer_panel_functions.php';
require_once __DIR__ . '/../includes/booking_expiration.php';

// Run expiration check if needed
runExpirationCheckIfNeeded();

// Check if user is customer
if (!isLoggedIn() || $_SESSION['user_role'] !== 'CUSTOMER') {
    redirect('/auth/login.php');
}

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'cancel_booking':
                    $bookingId = $_POST['booking_id'];
                    cancelCustomerBooking($_SESSION['user_id'], $bookingId);
                    $message = 'Booking cancelled successfully!';
                    $messageType = 'success';
                    break;
            }
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// Get customer dashboard data
$customerId = $_SESSION['user_id'];
$dashboardData = getCustomerDashboardData($customerId);

$pageTitle = "Customer Dashboard";

// Include customer header
include __DIR__ . '/../includes/customer_header.php';
?>

<!-- Page Header -->
<div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 rounded-2xl shadow-xl mb-8 hover-lift">
    <div class="px-6 sm:px-8 lg:px-10">
        <div class="py-8 md:flex md:items-center md:justify-between">
            <div class="flex-1 min-w-0">
                <div class="flex items-center">
                    <div>
                        <div class="flex items-center">
                            <h1 class="text-3xl font-bold leading-7 text-white sm:leading-9 sm:truncate font-serif">
                                Welcome back, <span class="text-salon-gold"><?= htmlspecialchars($dashboardData['profile']['name']) ?></span>
                            </h1>
                        </div>
                        <dl class="mt-3 flex flex-col sm:mt-2 sm:flex-row sm:flex-wrap">
                            <dt class="sr-only">Account status</dt>
                            <dd class="flex items-center text-sm text-gray-300 font-medium capitalize sm:mr-6">
                                <svg class="flex-shrink-0 mr-1.5 h-5 w-5 text-salon-gold" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Account Active
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="mt-6 flex space-x-3 md:mt-0 md:ml-4">
                <a href="<?= getBasePath() ?>/customer/book" class="inline-flex items-center px-6 py-3 border border-transparent shadow-lg text-sm font-semibold rounded-lg text-black bg-salon-gold hover:bg-gold-light focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-salon-gold transition-all duration-300 hover:scale-105">
                    <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Book Appointment
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Message Display -->
<?php if ($message): ?>
    <div class="mb-6 p-4 rounded-lg <?= $messageType === 'success' ? 'bg-salon-gold/10 border border-salon-gold text-salon-gold' : 'bg-red-500/10 border border-red-500 text-red-400' ?> backdrop-blur-sm">
        <?= htmlspecialchars($message) ?>
    </div>
<?php endif; ?>

<!-- Stats Overview -->
<div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-8">
    <!-- Points Balance -->
    <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 overflow-hidden shadow-xl rounded-2xl hover-lift">
        <div class="p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-salon-gold/20 rounded-full flex items-center justify-center">
                        <svg class="h-6 w-6 text-salon-gold" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-400 truncate">Points Balance</dt>
                        <dd class="text-2xl font-bold text-salon-gold"><?= number_format($dashboardData['pointsData']['currentPoints']) ?></dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <!-- Loyalty Tier -->
    <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 overflow-hidden shadow-xl rounded-2xl hover-lift">
        <div class="p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-salon-gold/20 rounded-full flex items-center justify-center">
                        <svg class="h-6 w-6 text-salon-gold" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-400 truncate">Loyalty Tier</dt>
                        <dd class="text-lg font-bold text-white"><?= $dashboardData['loyaltyTier']['name'] ?></dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <!-- Total Spent -->
    <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 overflow-hidden shadow-xl rounded-2xl hover-lift">
        <div class="p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-salon-gold/20 rounded-full flex items-center justify-center">
                        <svg class="h-6 w-6 text-salon-gold" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-400 truncate">Total Spent</dt>
                        <dd class="text-lg font-bold text-white"><?= CURRENCY_SYMBOL ?> <?= number_format($dashboardData['stats']['totalSpent'], 2) ?></dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <!-- Total Visits -->
    <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 overflow-hidden shadow-xl rounded-2xl hover-lift">
        <div class="p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-salon-gold/20 rounded-full flex items-center justify-center">
                        <svg class="h-6 w-6 text-salon-gold" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-400 truncate">Total Visits</dt>
                        <dd class="text-2xl font-bold text-white"><?= $dashboardData['stats']['completedBookings'] ?></dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content Grid -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
    <!-- Upcoming Appointments -->
    <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 overflow-hidden shadow-xl rounded-2xl hover-lift">
        <div class="p-6">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-xl font-bold text-white">Upcoming Appointments</h3>
                <a href="<?= getBasePath() ?>/customer/bookings" class="text-salon-gold hover:text-gold-light text-sm font-medium transition-colors duration-200">
                    View All <i class="fas fa-arrow-right ml-1"></i>
                </a>
            </div>

            <?php if (empty($dashboardData['upcomingBookings'])): ?>
                <div class="text-center py-12">
                    <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-calendar-plus text-2xl text-salon-gold"></i>
                    </div>
                    <p class="text-gray-400 mb-6 text-lg">No upcoming appointments</p>
                    <a href="<?= getBasePath() ?>/customer/book" class="bg-salon-gold hover:bg-gold-light text-black px-6 py-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105 shadow-lg">
                        Book Your First Appointment
                    </a>
                </div>
            <?php else: ?>
                <div class="space-y-4">
                    <?php foreach ($dashboardData['upcomingBookings'] as $booking): ?>
                        <div class="border border-secondary-700 rounded-xl p-5 bg-secondary-800/50 hover:bg-secondary-800/70 transition-all duration-300">
                            <div class="flex items-center justify-between mb-3">
                                <div>
                                    <h4 class="font-semibold text-white text-lg"><?= htmlspecialchars($booking['service_name']) ?></h4>
                                    <p class="text-sm text-gray-400">with <?= htmlspecialchars($booking['staff_name']) ?></p>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm font-semibold text-salon-gold"><?= date('M j, Y', strtotime($booking['date'])) ?></p>
                                    <p class="text-sm text-gray-400"><?= date('g:i A', strtotime($booking['start_time'])) ?></p>
                                </div>
                            </div>

                            <div class="flex items-center justify-between">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold
                                    <?php
                                    switch($booking['status']) {
                                        case 'PENDING': echo 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30'; break;
                                        case 'CONFIRMED': echo 'bg-salon-gold/20 text-salon-gold border border-salon-gold/30'; break;
                                        case 'IN_PROGRESS': echo 'bg-purple-500/20 text-purple-400 border border-purple-500/30'; break;
                                        default: echo 'bg-gray-500/20 text-gray-400 border border-gray-500/30';
                                    }
                                    ?>">
                                    <?= $booking['status'] ?>
                                </span>

                                <?php if ($booking['status'] === 'PENDING' || $booking['status'] === 'CONFIRMED'): ?>
                                    <form method="POST" class="inline" onsubmit="return confirm('Are you sure you want to cancel this appointment?')">
                                        <input type="hidden" name="action" value="cancel_booking">
                                        <input type="hidden" name="booking_id" value="<?= $booking['id'] ?>">
                                        <button type="submit" class="text-red-400 hover:text-red-300 text-sm font-medium transition-colors duration-200">
                                            <i class="fas fa-times mr-1"></i>Cancel
                                        </button>
                                    </form>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Loyalty Progress -->
    <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 overflow-hidden shadow-xl rounded-2xl hover-lift">
        <div class="p-6">
            <h3 class="text-xl font-bold text-white mb-6">Loyalty Progress</h3>

            <div class="mb-8">
                <div class="flex items-center justify-between mb-3">
                    <span class="text-sm font-semibold text-salon-gold">Current Tier: <?= $dashboardData['loyaltyTier']['name'] ?></span>
                    <?php if ($dashboardData['loyaltyTier']['nextTier']): ?>
                        <span class="text-sm text-gray-400">Next: <?= $dashboardData['loyaltyTier']['nextTier'] ?></span>
                    <?php endif; ?>
                </div>

                <?php if ($dashboardData['loyaltyTier']['nextTier']): ?>
                    <?php
                    $progress = (($dashboardData['stats']['totalSpent'] - $dashboardData['loyaltyTier']['minSpent']) /
                                ($dashboardData['loyaltyTier']['nextTierAmount'] + $dashboardData['stats']['totalSpent'] - $dashboardData['loyaltyTier']['minSpent'])) * 100;
                    ?>
                    <div class="w-full bg-secondary-800 rounded-full h-3 overflow-hidden">
                        <div class="bg-gradient-to-r from-salon-gold to-gold-light h-3 rounded-full transition-all duration-500" style="width: <?= min(100, max(0, $progress)) ?>%"></div>
                    </div>
                    <p class="text-sm text-gray-400 mt-2">
                        Spend <?= CURRENCY_SYMBOL ?> <?= number_format($dashboardData['loyaltyTier']['nextTierAmount'], 2) ?> more to reach <?= $dashboardData['loyaltyTier']['nextTier'] ?>
                    </p>
                <?php else: ?>
                    <div class="w-full bg-gradient-to-r from-salon-gold to-gold-light rounded-full h-3"></div>
                    <p class="text-sm text-salon-gold mt-2 font-medium">🎉 You've reached the highest tier!</p>
                <?php endif; ?>
            </div>

            <div class="space-y-3">
                <h4 class="font-semibold text-white">Your Benefits:</h4>
                <div class="space-y-2">
                    <?php foreach ($dashboardData['loyaltyTier']['benefits'] as $benefit): ?>
                        <div class="flex items-center gap-3 p-2 bg-secondary-800/50 rounded-lg">
                            <i class="fas fa-check text-salon-gold text-sm"></i>
                            <span class="text-sm text-gray-300"><?= htmlspecialchars($benefit) ?></span>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity & Points -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
    <!-- Recent Bookings -->
    <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 overflow-hidden shadow-xl rounded-2xl hover-lift">
        <div class="p-6">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-xl font-bold text-white">Recent Bookings</h3>
                <a href="<?= getBasePath() ?>/customer/bookings" class="text-salon-gold hover:text-gold-light text-sm font-medium transition-colors duration-200">
                    View All <i class="fas fa-arrow-right ml-1"></i>
                </a>
            </div>

            <?php if (empty($dashboardData['recentBookings'])): ?>
                <div class="text-center py-12">
                    <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-history text-2xl text-salon-gold"></i>
                    </div>
                    <p class="text-gray-400 text-lg">No booking history yet</p>
                </div>
            <?php else: ?>
                <div class="space-y-4">
                    <?php foreach (array_slice($dashboardData['recentBookings'], 0, 5) as $booking): ?>
                        <div class="flex items-center justify-between border-b border-secondary-700 pb-4 last:border-b-0 last:pb-0">
                            <div class="flex-1">
                                <h4 class="font-semibold text-white"><?= htmlspecialchars($booking['service_name']) ?></h4>
                                <p class="text-sm text-gray-400"><?= date('M j, Y', strtotime($booking['date'])) ?> • <?= htmlspecialchars($booking['staff_name']) ?></p>
                            </div>
                            <div class="text-right ml-4">
                                <p class="text-sm font-semibold text-salon-gold"><?= CURRENCY_SYMBOL ?> <?= number_format($booking['total_amount'], 2) ?></p>
                                <span class="text-xs px-2 py-1 rounded-full font-medium
                                    <?php
                                    switch($booking['status']) {
                                        case 'COMPLETED': echo 'bg-green-500/20 text-green-400 border border-green-500/30'; break;
                                        case 'CANCELLED': echo 'bg-red-500/20 text-red-400 border border-red-500/30'; break;
                                        case 'PENDING': echo 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30'; break;
                                        default: echo 'bg-gray-500/20 text-gray-400 border border-gray-500/30';
                                    }
                                    ?>">
                                    <?= $booking['status'] ?>
                                </span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Points Activity -->
    <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 overflow-hidden shadow-xl rounded-2xl hover-lift">
        <div class="p-6">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-xl font-bold text-white">Points Activity</h3>
                <a href="<?= getBasePath() ?>/customer/rewards" class="text-salon-gold hover:text-gold-light text-sm font-medium transition-colors duration-200">
                    View All <i class="fas fa-arrow-right ml-1"></i>
                </a>
            </div>

            <div class="grid grid-cols-2 gap-6 mb-6">
                <div class="text-center p-4 bg-secondary-800/50 rounded-xl">
                    <p class="text-3xl font-bold text-salon-gold">+<?= number_format($dashboardData['pointsData']['monthlyEarned']) ?></p>
                    <p class="text-sm text-gray-400 mt-1">Earned This Month</p>
                </div>
                <div class="text-center p-4 bg-secondary-800/50 rounded-xl">
                    <p class="text-3xl font-bold text-red-400">-<?= number_format($dashboardData['pointsData']['monthlyRedeemed']) ?></p>
                    <p class="text-sm text-gray-400 mt-1">Redeemed This Month</p>
                </div>
            </div>

            <?php if (empty($dashboardData['pointsData']['transactions'])): ?>
                <div class="text-center py-8">
                    <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-coins text-2xl text-salon-gold"></i>
                    </div>
                    <p class="text-gray-400">No point transactions yet</p>
                </div>
            <?php else: ?>
                <div class="space-y-3">
                    <?php foreach (array_slice($dashboardData['pointsData']['transactions'], 0, 5) as $transaction): ?>
                        <div class="flex items-center justify-between p-3 bg-secondary-800/50 rounded-lg">
                            <div class="flex-1">
                                <p class="text-white font-medium"><?= htmlspecialchars($transaction['description']) ?></p>
                                <p class="text-xs text-gray-400"><?= date('M j, Y', strtotime($transaction['created_at'])) ?></p>
                            </div>
                            <span class="font-bold text-lg <?= $transaction['type'] === 'EARNED' ? 'text-salon-gold' : 'text-red-400' ?>">
                                <?= $transaction['type'] === 'EARNED' ? '+' : '' ?><?= number_format($transaction['points']) ?>
                            </span>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include __DIR__ . '/../includes/customer_footer.php'; ?>
