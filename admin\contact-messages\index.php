<?php
/**
 * Admin Contact Messages Management
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/contact_functions.php';

// Check if user is admin
if (!isLoggedIn() || !hasRole('ADMIN')) {
    redirect('/admin/login');
}

// Handle bulk actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['bulk_action'])) {
    $action = $_POST['bulk_action'];
    $selectedIds = $_POST['selected_messages'] ?? [];
    
    if (!empty($selectedIds) && !empty($action)) {
        $result = bulkUpdateContactMessages($selectedIds, $action);
        
        if ($result) {
            $actionText = [
                'mark_read' => 'marked as read',
                'archive' => 'archived',
                'delete' => 'deleted'
            ];
            
            $_SESSION['success_message'] = count($selectedIds) . ' message(s) ' . ($actionText[$action] ?? 'updated') . ' successfully.';
        } else {
            $_SESSION['error_message'] = 'Failed to perform bulk action.';
        }
        
        redirect('/admin/contact-messages');
    }
}

// Get filters
$filters = [
    'status' => $_GET['status'] ?? '',
    'search' => $_GET['search'] ?? '',
    'date_from' => $_GET['date_from'] ?? '',
    'date_to' => $_GET['date_to'] ?? '',
    'subject' => $_GET['subject'] ?? ''
];

$page = (int)($_GET['page'] ?? 1);
$limit = 20;

// Get contact messages
$result = getContactMessages($filters, $page, $limit);
$messages = $result['messages'];
$totalPages = $result['pages'];
$totalMessages = $result['total'];

// Get statistics
$stats = getContactMessageStats();

// Get unique subjects for filter
$subjects = getContactSubjects();

$pageTitle = "Contact Messages";
include __DIR__ . '/../../includes/admin_header.php';
?>

<div class="min-h-screen bg-salon-black pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="sm:flex sm:items-center sm:justify-between mb-8">
                <div>
                    <h1 class="text-3xl font-bold text-white">Contact Messages</h1>
                    <p class="mt-2 text-gray-300">Manage customer inquiries and messages</p>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-secondary-800 rounded-lg p-6 border border-secondary-700">
                    <div class="flex items-center">
                        <div class="p-2 bg-blue-500/20 rounded-lg">
                            <i class="fas fa-envelope text-blue-400 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-400">Total Messages</p>
                            <p class="text-2xl font-bold text-white"><?= number_format($stats['total']) ?></p>
                        </div>
                    </div>
                </div>

                <div class="bg-secondary-800 rounded-lg p-6 border border-secondary-700">
                    <div class="flex items-center">
                        <div class="p-2 bg-yellow-500/20 rounded-lg">
                            <i class="fas fa-exclamation-circle text-yellow-400 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-400">New Messages</p>
                            <p class="text-2xl font-bold text-white"><?= number_format($stats['new']) ?></p>
                        </div>
                    </div>
                </div>

                <div class="bg-secondary-800 rounded-lg p-6 border border-secondary-700">
                    <div class="flex items-center">
                        <div class="p-2 bg-green-500/20 rounded-lg">
                            <i class="fas fa-reply text-green-400 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-400">Replied</p>
                            <p class="text-2xl font-bold text-white"><?= number_format($stats['replied']) ?></p>
                        </div>
                    </div>
                </div>

                <div class="bg-secondary-800 rounded-lg p-6 border border-secondary-700">
                    <div class="flex items-center">
                        <div class="p-2 bg-salon-gold/20 rounded-lg">
                            <i class="fas fa-calendar-day text-salon-gold text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-400">Today</p>
                            <p class="text-2xl font-bold text-white"><?= number_format($stats['today']) ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="bg-secondary-800 rounded-lg p-6 border border-secondary-700 mb-8">
                <form method="GET" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Status</label>
                        <select name="status" class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                            <option value="">All Statuses</option>
                            <option value="NEW" <?= $filters['status'] === 'NEW' ? 'selected' : '' ?>>New</option>
                            <option value="READ" <?= $filters['status'] === 'READ' ? 'selected' : '' ?>>Read</option>
                            <option value="REPLIED" <?= $filters['status'] === 'REPLIED' ? 'selected' : '' ?>>Replied</option>
                            <option value="ARCHIVED" <?= $filters['status'] === 'ARCHIVED' ? 'selected' : '' ?>>Archived</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Subject</label>
                        <select name="subject" class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                            <option value="">All Subjects</option>
                            <?php foreach ($subjects as $subject): ?>
                                <option value="<?= htmlspecialchars($subject['subject']) ?>" <?= $filters['subject'] === $subject['subject'] ? 'selected' : '' ?>>
                                    <?= ucfirst(htmlspecialchars($subject['subject'])) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">From Date</label>
                        <input type="date" name="date_from" value="<?= htmlspecialchars($filters['date_from']) ?>"
                               class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">To Date</label>
                        <input type="date" name="date_to" value="<?= htmlspecialchars($filters['date_to']) ?>"
                               class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Search</label>
                        <div class="flex gap-2">
                            <input type="text" name="search" value="<?= htmlspecialchars($filters['search']) ?>"
                                   placeholder="Search messages..."
                                   class="flex-1 px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                            <button type="submit" class="px-4 py-2 bg-salon-gold hover:bg-yellow-500 text-black rounded-lg font-semibold transition-colors">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </form>

                <?php if (array_filter($filters)): ?>
                    <div class="mt-4 pt-4 border-t border-secondary-700">
                        <a href="<?= getBasePath() ?>/admin/contact-messages" class="text-salon-gold hover:text-yellow-400 text-sm">
                            <i class="fas fa-times mr-1"></i>Clear Filters
                        </a>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Messages Table -->
            <div class="bg-secondary-800 rounded-lg border border-secondary-700 overflow-hidden">
                <?php if (!empty($messages)): ?>
                    <form method="POST" id="bulkForm">
                        <!-- Bulk Actions -->
                        <div class="p-4 border-b border-secondary-700 flex items-center justify-between">
                            <div class="flex items-center gap-4">
                                <label class="flex items-center">
                                    <input type="checkbox" id="selectAll" class="rounded border-secondary-600 text-salon-gold focus:ring-salon-gold focus:ring-offset-0 bg-secondary-700">
                                    <span class="ml-2 text-sm text-gray-300">Select All</span>
                                </label>
                                
                                <select name="bulk_action" id="bulkAction" class="px-3 py-1 bg-secondary-700 border border-secondary-600 rounded text-white text-sm">
                                    <option value="">Bulk Actions</option>
                                    <option value="mark_read">Mark as Read</option>
                                    <option value="archive">Archive</option>
                                    <option value="delete">Delete</option>
                                </select>
                                
                                <button type="submit" id="bulkSubmit" class="px-3 py-1 bg-salon-gold hover:bg-yellow-500 text-black rounded text-sm font-semibold transition-colors disabled:opacity-50" disabled>
                                    Apply
                                </button>
                            </div>
                            
                            <div class="text-sm text-gray-400">
                                Showing <?= count($messages) ?> of <?= number_format($totalMessages) ?> messages
                            </div>
                        </div>

                        <!-- Table -->
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead class="bg-secondary-700">
                                    <tr>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                            <input type="checkbox" class="rounded border-secondary-600 text-salon-gold focus:ring-salon-gold focus:ring-offset-0 bg-secondary-700">
                                        </th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">From</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Subject</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Message</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Date</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-secondary-700">
                                    <?php foreach ($messages as $message): ?>
                                        <tr class="hover:bg-secondary-700/50 transition-colors" onclick="event.stopPropagation()">
                                            <td class="px-4 py-4">
                                                <input type="checkbox" name="selected_messages[]" value="<?= $message['id'] ?>" 
                                                       class="message-checkbox rounded border-secondary-600 text-salon-gold focus:ring-salon-gold focus:ring-offset-0 bg-secondary-700">
                                            </td>
                                            <td class="px-4 py-4">
                                                <?php
                                                $statusColors = [
                                                    'NEW' => 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30',
                                                    'READ' => 'bg-blue-500/20 text-blue-400 border-blue-500/30',
                                                    'REPLIED' => 'bg-green-500/20 text-green-400 border-green-500/30',
                                                    'ARCHIVED' => 'bg-gray-500/20 text-gray-400 border-gray-500/30'
                                                ];
                                                $statusClass = $statusColors[$message['status']] ?? 'bg-gray-500/20 text-gray-400 border-gray-500/30';
                                                ?>
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border <?= $statusClass ?>">
                                                    <?= htmlspecialchars($message['status']) ?>
                                                </span>
                                            </td>
                                            <td class="px-4 py-4">
                                                <div class="text-sm">
                                                    <div class="font-medium text-white"><?= htmlspecialchars($message['name']) ?></div>
                                                    <div class="text-gray-400"><?= htmlspecialchars($message['email']) ?></div>
                                                    <?php if ($message['phone']): ?>
                                                        <div class="text-gray-400 text-xs"><?= htmlspecialchars($message['phone']) ?></div>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td class="px-4 py-4">
                                                <span class="text-sm text-white">
                                                    <?= $message['subject'] ? ucfirst(htmlspecialchars($message['subject'])) : '<span class="text-gray-400">No subject</span>' ?>
                                                </span>
                                            </td>
                                            <td class="px-4 py-4">
                                                <div class="text-sm text-gray-300 max-w-xs">
                                                    <?= htmlspecialchars(substr($message['message'], 0, 100)) ?><?= strlen($message['message']) > 100 ? '...' : '' ?>
                                                </div>
                                            </td>
                                            <td class="px-4 py-4">
                                                <div class="text-sm text-gray-300">
                                                    <?= date('M j, Y', strtotime($message['created_at'])) ?>
                                                    <div class="text-xs text-gray-400">
                                                        <?= date('g:i A', strtotime($message['created_at'])) ?>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="px-4 py-4">
                                                <div class="flex items-center space-x-1">
                                                    <!-- View Button -->
                                                    <button type="button" onclick="event.stopPropagation(); viewMessage('<?= $message['id'] ?>')"
                                                            class="inline-flex items-center px-2 py-1 text-xs font-medium rounded text-salon-gold bg-salon-gold/10 hover:bg-salon-gold/20 transition-colors"
                                                            title="View Message">
                                                        <i class="fas fa-eye mr-1"></i>
                                                        View
                                                    </button>

                                                    <!-- Mark as Read Button (only for NEW messages) -->
                                                    <?php if ($message['status'] === 'NEW'): ?>
                                                        <button type="button" onclick="event.stopPropagation(); markAsRead('<?= $message['id'] ?>')"
                                                                class="inline-flex items-center px-2 py-1 text-xs font-medium rounded text-blue-400 bg-blue-400/10 hover:bg-blue-400/20 transition-colors"
                                                                title="Mark as Read">
                                                            <i class="fas fa-check mr-1"></i>
                                                            Read
                                                        </button>
                                                    <?php endif; ?>

                                                    <!-- Reply Button (only for non-replied messages) -->
                                                    <?php if ($message['status'] !== 'REPLIED'): ?>
                                                        <button type="button" onclick="event.stopPropagation(); replyToMessage('<?= $message['id'] ?>')"
                                                                class="inline-flex items-center px-2 py-1 text-xs font-medium rounded text-green-400 bg-green-400/10 hover:bg-green-400/20 transition-colors"
                                                                title="Reply">
                                                            <i class="fas fa-reply mr-1"></i>
                                                            Reply
                                                        </button>
                                                    <?php endif; ?>

                                                    <!-- Delete Button -->
                                                    <button type="button" onclick="event.stopPropagation(); deleteMessage('<?= $message['id'] ?>')"
                                                            class="inline-flex items-center px-2 py-1 text-xs font-medium rounded text-red-400 bg-red-400/10 hover:bg-red-400/20 transition-colors"
                                                            title="Delete Message">
                                                        <i class="fas fa-trash mr-1"></i>
                                                        Delete
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </form>
                <?php else: ?>
                    <div class="p-12 text-center">
                        <i class="fas fa-inbox text-gray-500 text-6xl mb-4"></i>
                        <h3 class="text-xl font-semibold text-white mb-2">No messages found</h3>
                        <p class="text-gray-400">
                            <?= array_filter($filters) ? 'Try adjusting your filters to see more results.' : 'No contact messages have been received yet.' ?>
                        </p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <div class="mt-8 flex items-center justify-between">
                    <div class="text-sm text-gray-400">
                        Showing page <?= $page ?> of <?= $totalPages ?>
                    </div>
                    
                    <nav class="flex items-center space-x-2">
                        <?php if ($page > 1): ?>
                            <a href="?<?= http_build_query(array_merge($filters, ['page' => $page - 1])) ?>" 
                               class="px-3 py-2 bg-secondary-700 text-white rounded-lg hover:bg-secondary-600 transition-colors">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        <?php endif; ?>
                        
                        <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                            <a href="?<?= http_build_query(array_merge($filters, ['page' => $i])) ?>" 
                               class="px-3 py-2 rounded-lg transition-colors <?= $i === $page ? 'bg-salon-gold text-black' : 'bg-secondary-700 text-white hover:bg-secondary-600' ?>">
                                <?= $i ?>
                            </a>
                        <?php endfor; ?>
                        
                        <?php if ($page < $totalPages): ?>
                            <a href="?<?= http_build_query(array_merge($filters, ['page' => $page + 1])) ?>" 
                               class="px-3 py-2 bg-secondary-700 text-white rounded-lg hover:bg-secondary-600 transition-colors">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        <?php endif; ?>
                    </nav>
                </div>
            <?php endif; ?>
                </div>
            </main>
        </div>
    </div>
</div>

<!-- Success/Error Messages -->
<?php if (isset($_SESSION['success_message'])): ?>
    <div id="successMessage" class="fixed top-4 right-4 bg-green-600 text-white px-6 py-3 rounded-lg shadow-lg z-50">
        <div class="flex items-center">
            <i class="fas fa-check-circle mr-2"></i>
            <?= htmlspecialchars($_SESSION['success_message']) ?>
        </div>
    </div>
    <?php unset($_SESSION['success_message']); ?>
<?php endif; ?>

<?php if (isset($_SESSION['error_message'])): ?>
    <div id="errorMessage" class="fixed top-4 right-4 bg-red-600 text-white px-6 py-3 rounded-lg shadow-lg z-50">
        <div class="flex items-center">
            <i class="fas fa-exclamation-triangle mr-2"></i>
            <?= htmlspecialchars($_SESSION['error_message']) ?>
        </div>
    </div>
    <?php unset($_SESSION['error_message']); ?>
<?php endif; ?>

<!-- View Message Modal -->
<div id="viewMessageModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50" tabindex="-1">
    <div class="flex items-center justify-center min-h-screen p-4" onclick="closeModal('viewMessageModal')">
        <div class="bg-secondary-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto" onclick="event.stopPropagation()">
            <div class="p-6 border-b border-secondary-700">
                <div class="flex items-center justify-between">
                    <h3 class="text-xl font-semibold text-white">Message Details</h3>
                    <button type="button" onclick="closeModal('viewMessageModal')" class="text-gray-400 hover:text-white transition-colors">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <div id="messageContent" class="p-6">
                <!-- Message content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- Reply Modal -->
<div id="replyModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50" tabindex="-1">
    <div class="flex items-center justify-center min-h-screen p-4" onclick="closeModal('replyModal')">
        <div class="bg-secondary-800 rounded-lg max-w-2xl w-full" onclick="event.stopPropagation()">
            <div class="p-6 border-b border-secondary-700">
                <div class="flex items-center justify-between">
                    <h3 class="text-xl font-semibold text-white">Reply to Message</h3>
                    <button type="button" onclick="closeModal('replyModal')" class="text-gray-400 hover:text-white transition-colors">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <form id="replyForm" class="p-6">
                <input type="hidden" id="replyMessageId" name="message_id">

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-300 mb-2">Reply Message</label>
                    <textarea id="replyMessage" name="reply_message" rows="6" required
                              class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold"
                              placeholder="Type your reply here..."></textarea>
                </div>

                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeModal('replyModal')"
                            class="px-4 py-2 bg-secondary-700 text-white rounded-lg hover:bg-secondary-600 transition-colors">
                        Cancel
                    </button>
                    <button type="submit"
                            class="px-4 py-2 bg-salon-gold text-black rounded-lg hover:bg-yellow-500 transition-colors">
                        <i class="fas fa-reply mr-2"></i>Send Reply
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Custom Confirmation Modal -->
<div id="confirmationModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50" tabindex="-1">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-secondary-800 rounded-lg max-w-md w-full" onclick="event.stopPropagation()">
            <div class="p-6 border-b border-secondary-700">
                <div class="flex items-center justify-between">
                    <h3 id="confirmTitle" class="text-xl font-semibold text-white">Confirm Action</h3>
                    <button type="button" onclick="closeConfirmation()" class="text-gray-400 hover:text-white transition-colors">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <div class="p-6">
                <div id="confirmMessage" class="text-gray-300 mb-6 whitespace-pre-line">
                    <!-- Confirmation message will be inserted here -->
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeConfirmation()"
                            class="px-4 py-2 bg-secondary-700 text-white rounded-lg hover:bg-secondary-600 transition-colors">
                        Cancel
                    </button>
                    <button type="button" id="confirmButton" onclick="executeConfirmedAction()"
                            class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                        <i class="fas fa-trash mr-2"></i>Delete
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-hide success/error messages
    setTimeout(() => {
        const successMsg = document.getElementById('successMessage');
        const errorMsg = document.getElementById('errorMessage');
        if (successMsg) successMsg.style.display = 'none';
        if (errorMsg) errorMsg.style.display = 'none';
    }, 5000);

    // Add escape key functionality for modals
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            // Close confirmation modal if open
            const confirmModal = document.getElementById('confirmationModal');
            if (confirmModal && !confirmModal.classList.contains('hidden')) {
                closeConfirmation();
                return;
            }

            // Close view modal if open
            const viewModal = document.getElementById('viewMessageModal');
            if (viewModal && !viewModal.classList.contains('hidden')) {
                closeModal('viewMessageModal');
                return;
            }

            // Close reply modal if open
            const replyModal = document.getElementById('replyModal');
            if (replyModal && !replyModal.classList.contains('hidden')) {
                closeModal('replyModal');
                return;
            }
        }
    });

    // Bulk actions functionality
    const selectAllCheckbox = document.getElementById('selectAll');
    const messageCheckboxes = document.querySelectorAll('.message-checkbox');
    const bulkAction = document.getElementById('bulkAction');
    const bulkSubmit = document.getElementById('bulkSubmit');

    // Select all functionality
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            messageCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkActions();
        });
    }

    // Individual checkbox change
    messageCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActions);
    });

    function updateBulkActions() {
        const checkedBoxes = document.querySelectorAll('.message-checkbox:checked');
        const hasSelection = checkedBoxes.length > 0;
        const hasAction = bulkAction.value !== '';

        bulkSubmit.disabled = !(hasSelection && hasAction);

        // Update select all checkbox state
        if (selectAllCheckbox) {
            selectAllCheckbox.checked = checkedBoxes.length === messageCheckboxes.length;
            selectAllCheckbox.indeterminate = checkedBoxes.length > 0 && checkedBoxes.length < messageCheckboxes.length;
        }
    }

    // Bulk action change
    if (bulkAction) {
        bulkAction.addEventListener('change', updateBulkActions);
    }

    // Bulk form submission
    const bulkForm = document.getElementById('bulkForm');
    if (bulkForm) {
        bulkForm.addEventListener('submit', function(e) {
            const checkedBoxes = document.querySelectorAll('.message-checkbox:checked');
            const action = bulkAction.value;

            if (checkedBoxes.length === 0) {
                e.preventDefault();
                alert('Please select at least one message.');
                return;
            }

            if (!action) {
                e.preventDefault();
                alert('Please select an action.');
                return;
            }

            const actionText = {
                'mark_read': 'mark as read',
                'archive': 'archive',
                'delete': 'permanently delete'
            };

            const actionIcons = {
                'mark_read': '📖',
                'archive': '📁',
                'delete': '🗑️'
            };

            let confirmText;

            if (action === 'delete') {
                confirmText = `${actionIcons[action]} BULK DELETE CONTACT MESSAGES\n\n` +
                    `You are about to permanently delete ${checkedBoxes.length} contact message(s).\n\n` +
                    `⚠️ WARNING: This action cannot be undone!\n` +
                    `• All selected messages will be permanently removed\n` +
                    `• Customer information and message content will be lost\n` +
                    `• This action cannot be reversed\n\n` +
                    `Are you absolutely sure you want to proceed?`;
            } else {
                confirmText = `${actionIcons[action]} BULK ACTION CONFIRMATION\n\n` +
                    `You are about to ${actionText[action]} ${checkedBoxes.length} contact message(s).\n\n` +
                    `Do you want to proceed with this action?`;
            }

            if (!confirm(confirmText)) {
                e.preventDefault();
            }
        });
    }
});

// View message function
function viewMessage(messageId) {
    fetch(`<?= getBasePath() ?>/api/admin/contact-messages.php?action=get&id=${messageId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const message = data.message;
                const content = document.getElementById('messageContent');

                content.innerHTML = `
                    <div class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h4 class="text-sm font-medium text-gray-400 mb-2">From</h4>
                                <div class="text-white">
                                    <div class="font-semibold">${escapeHtml(message.name)}</div>
                                    <div class="text-gray-300">${escapeHtml(message.email)}</div>
                                    ${message.phone ? `<div class="text-gray-300 text-sm">${escapeHtml(message.phone)}</div>` : ''}
                                </div>
                            </div>

                            <div>
                                <h4 class="text-sm font-medium text-gray-400 mb-2">Details</h4>
                                <div class="text-white">
                                    <div><span class="text-gray-400">Subject:</span> ${message.subject ? escapeHtml(message.subject) : 'No subject'}</div>
                                    <div><span class="text-gray-400">Status:</span> <span class="capitalize">${escapeHtml(message.status.toLowerCase())}</span></div>
                                    <div><span class="text-gray-400">Date:</span> ${new Date(message.created_at).toLocaleString()}</div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <h4 class="text-sm font-medium text-gray-400 mb-2">Message</h4>
                            <div class="bg-secondary-700 rounded-lg p-4 text-white whitespace-pre-wrap">${escapeHtml(message.message)}</div>
                        </div>

                        <div class="flex justify-end space-x-3">
                            ${message.status !== 'REPLIED' ? `
                                <button onclick="closeModal('viewMessageModal'); replyToMessage('${message.id}')"
                                        class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                    <i class="fas fa-reply mr-2"></i>Reply
                                </button>
                            ` : ''}

                            <button onclick="updateMessageStatus('${message.id}', 'READ')"
                                    class="px-4 py-2 bg-salon-gold text-black rounded-lg hover:bg-yellow-500 transition-colors">
                                <i class="fas fa-eye mr-2"></i>Mark as Read
                            </button>
                        </div>
                    </div>
                `;

                document.getElementById('viewMessageModal').classList.remove('hidden');

                // Focus the modal for keyboard navigation
                document.getElementById('viewMessageModal').focus();
            } else {
                alert('Failed to load message details.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to load message details.');
        });
}

// Reply to message function
function replyToMessage(messageId) {
    document.getElementById('replyMessageId').value = messageId;
    document.getElementById('replyMessage').value = '';
    document.getElementById('replyModal').classList.remove('hidden');

    // Focus the textarea for immediate typing
    setTimeout(() => {
        document.getElementById('replyMessage').focus();
    }, 100);
}

// Mark message as read function
function markAsRead(messageId) {
    fetch(`<?= getBasePath() ?>/api/admin/contact-messages.php`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'update_status',
            id: messageId,
            status: 'READ'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success notification
            showNotification('Message marked as read successfully!', 'success');
            // Reload page to update the UI
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showNotification('Failed to mark message as read.', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Failed to mark message as read.', 'error');
    });
}

// Update message status
function updateMessageStatus(messageId, status) {
    fetch(`<?= getBasePath() ?>/api/admin/contact-messages.php`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'update_status',
            id: messageId,
            status: status
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Failed to update message status.');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to update message status.');
    });
}

// Delete message function
function deleteMessage(messageId) {
    // First get the message details to show in confirmation
    fetch(`<?= getBasePath() ?>/api/admin/contact-messages.php?action=get&id=${messageId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const message = data.message;
                const confirmTitle = '🗑️ Delete Contact Message';
                const confirmMessage = `You are about to permanently delete this contact message:

From: ${message.name}
Email: ${message.email}
Subject: ${message.subject || 'No subject'}
Date: ${new Date(message.created_at).toLocaleDateString()}
Status: ${message.status}

⚠️ WARNING: This action cannot be undone!
The customer's message and all associated data will be permanently removed from the system.

Are you sure you want to proceed?`;

                showCustomConfirmation(
                    confirmTitle,
                    confirmMessage,
                    '<i class="fas fa-trash mr-2"></i>Delete Message',
                    'bg-red-600 text-white hover:bg-red-700',
                    () => performDelete(messageId)
                );
            } else {
                // Fallback to simple confirmation if we can't get message details
                showCustomConfirmation(
                    '🗑️ Delete Contact Message',
                    'Are you sure you want to delete this contact message?\n\n⚠️ This action cannot be undone and will permanently remove the customer\'s message from the system.',
                    '<i class="fas fa-trash mr-2"></i>Delete Message',
                    'bg-red-600 text-white hover:bg-red-700',
                    () => performDelete(messageId)
                );
            }
        })
        .catch(error => {
            console.error('Error:', error);
            // Fallback to simple confirmation on error
            showCustomConfirmation(
                '🗑️ Delete Contact Message',
                'Are you sure you want to delete this contact message?\n\n⚠️ This action cannot be undone and will permanently remove the customer\'s message from the system.',
                '<i class="fas fa-trash mr-2"></i>Delete Message',
                'bg-red-600 text-white hover:bg-red-700',
                () => performDelete(messageId)
            );
        });
}

// Perform the actual deletion
function performDelete(messageId) {
    fetch(`<?= getBasePath() ?>/api/admin/contact-messages.php`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'delete',
            id: messageId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Contact message deleted successfully!', 'success');
            // Reload page to update the UI
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showNotification('Failed to delete message: ' + (data.error || 'Unknown error'), 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Failed to delete message. Please try again.', 'error');
    });
}

// Close modal function
function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
}

// Reply form submission
document.getElementById('replyForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const messageId = document.getElementById('replyMessageId').value;
    const replyMessage = document.getElementById('replyMessage').value;

    if (!replyMessage.trim()) {
        alert('Please enter a reply message.');
        return;
    }

    fetch(`<?= getBasePath() ?>/api/admin/contact-messages.php`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'reply',
            id: messageId,
            reply_message: replyMessage
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeModal('replyModal');
            showNotification('Reply sent successfully!', 'success');
            // Reload page to update the UI
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showNotification('Failed to send reply: ' + (data.error || 'Unknown error'), 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Failed to send reply.', 'error');
    });
});

// Show notification function
function showNotification(message, type = 'success') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.notification-toast');
    existingNotifications.forEach(notification => notification.remove());

    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification-toast fixed top-4 right-4 px-6 py-3 rounded-lg shadow-lg z-50 flex items-center transition-all duration-300 transform translate-x-full`;

    // Set colors based on type
    if (type === 'success') {
        notification.className += ' bg-green-600 text-white';
        notification.innerHTML = `<i class="fas fa-check-circle mr-2"></i>${message}`;
    } else if (type === 'error') {
        notification.className += ' bg-red-600 text-white';
        notification.innerHTML = `<i class="fas fa-exclamation-triangle mr-2"></i>${message}`;
    } else {
        notification.className += ' bg-blue-600 text-white';
        notification.innerHTML = `<i class="fas fa-info-circle mr-2"></i>${message}`;
    }

    // Add to page
    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // Auto remove after 3 seconds
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Custom confirmation modal functions
let confirmationCallback = null;

function showCustomConfirmation(title, message, buttonText, buttonClass, callback) {
    document.getElementById('confirmTitle').textContent = title;
    document.getElementById('confirmMessage').textContent = message;

    const confirmButton = document.getElementById('confirmButton');
    confirmButton.innerHTML = buttonText;
    confirmButton.className = `px-4 py-2 rounded-lg transition-colors ${buttonClass}`;

    confirmationCallback = callback;
    document.getElementById('confirmationModal').classList.remove('hidden');

    // Focus the modal
    setTimeout(() => {
        document.getElementById('confirmationModal').focus();
    }, 100);
}

function closeConfirmation() {
    document.getElementById('confirmationModal').classList.add('hidden');
    confirmationCallback = null;
}

function executeConfirmedAction() {
    if (confirmationCallback) {
        confirmationCallback();
    }
    closeConfirmation();
}

// Utility function to escape HTML
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>
