<?php
/**
 * Offers Page
 * Flix Salon & SPA - PHP Version
 */

require_once __DIR__ . '/config/app.php';

// Get all active offers
$offers = $database->fetchAll("
    SELECT * FROM offers
    WHERE is_active = 1 AND valid_from <= NOW() AND valid_to >= NOW()
    ORDER BY discount DESC, created_at DESC
");

$pageTitle = "Special Offers";
include __DIR__ . '/includes/header.php';
?>

    <!-- Hero Section -->
    <section class="relative py-24 bg-gradient-to-br from-salon-black via-salon-black to-salon-black">
        <div class="absolute inset-0 bg-gradient-to-r from-salon-gold/5 to-transparent"></div>
        <div class="relative max-w-7xl mx-auto px-6 text-center">
            <div class="inline-block bg-salon-gold/10 text-salon-gold px-4 py-2 rounded-full text-sm font-semibold mb-6">
                Limited Time Deals
            </div>
            <h1 class="text-5xl md:text-7xl font-bold font-serif text-white mb-6">
                Special <span class="text-salon-gold">Offers</span>
            </h1>
            <p class="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
                Don't miss out on our exclusive deals and promotions. Save big on your favorite beauty services and treatments.
            </p>
        </div>
    </section>

    <!-- Offers Grid -->
    <section class="py-24 bg-salon-black">
        <div class="max-w-7xl mx-auto px-6">
            <?php if (empty($offers)): ?>
                <!-- No Active Offers -->
                <div class="text-center py-16">
                    <i class="fas fa-tags text-6xl text-gray-600 mb-6"></i>
                    <h3 class="text-2xl font-bold text-white mb-4">No Active Offers</h3>
                    <p class="text-gray-400 mb-8">Check back soon for amazing deals and promotions!</p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <a href="<?= getBasePath() ?>/services.php" class="bg-salon-gold hover:bg-yellow-500 text-black px-8 py-3 rounded-lg font-semibold transition-colors">
                            View Services
                        </a>
                        <a href="<?= getBasePath() ?>/packages.php" class="bg-secondary-900 hover:bg-secondary-800 text-white px-8 py-3 rounded-lg font-semibold transition-colors border border-secondary-700">
                            View Packages
                        </a>
                    </div>
                </div>
            <?php else: ?>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <?php foreach ($offers as $index => $offer): ?>
                        <div class="group relative">
                            <?php if ($offer['discount'] >= 30): ?>
                                <!-- Hot Deal Badge for high discounts -->
                                <div class="absolute -top-4 left-4 z-10">
                                    <div class="bg-red-500 text-white px-3 py-1 rounded-full text-sm font-bold animate-pulse">
                                        🔥 Hot Deal
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 rounded-2xl overflow-hidden hover:border-salon-gold/50 transition-all duration-300 hover:scale-105 hover:shadow-xl">
                                <!-- Offer Header -->
                                <div class="relative h-48 bg-gradient-to-br from-salon-gold/20 to-secondary-900 overflow-hidden">
                                    <?php if ($offer['image']): ?>
                                        <img src="<?= htmlspecialchars($offer['image']) ?>" alt="<?= htmlspecialchars($offer['title']) ?>" class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                                    <?php else: ?>
                                        <div class="w-full h-full flex items-center justify-center">
                                            <div class="text-center">
                                                <i class="fas fa-percent text-salon-gold/60 text-4xl mb-2"></i>
                                                <div class="text-gray-300 text-sm">Special Offer</div>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                    <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                                    
                                    <!-- Discount Badge -->
                                    <div class="absolute top-4 right-4">
                                        <div class="bg-salon-gold text-black px-3 py-1 rounded-full text-lg font-bold">
                                            <?= $offer['discount'] ?>% OFF
                                        </div>
                                    </div>
                                </div>

                                <!-- Offer Content -->
                                <div class="p-6">
                                    <h3 class="text-2xl font-bold text-white mb-2 group-hover:text-salon-gold transition-colors duration-300">
                                        <?= htmlspecialchars($offer['title']) ?>
                                    </h3>
                                    
                                    <p class="text-gray-300 mb-4 leading-relaxed">
                                        <?= htmlspecialchars($offer['description']) ?>
                                    </p>
                                    
                                    <!-- Offer Details -->
                                    <div class="space-y-3 mb-4">
                                        <div>
                                            <h4 class="text-sm font-semibold text-salon-gold mb-1">Offer Details:</h4>
                                            <p class="text-sm text-gray-300">Valid for all services. Cannot be combined with other offers.</p>
                                        </div>
                                    </div>
                                    
                                    <!-- Validity Period -->
                                    <div class="flex items-center justify-between mb-4 text-sm">
                                        <div class="flex items-center text-gray-400">
                                            <i class="fas fa-calendar mr-2"></i>
                                            Valid until <?= date('M j, Y', strtotime($offer['valid_to'])) ?>
                                        </div>
                                        <?php
                                        $daysLeft = ceil((strtotime($offer['valid_to']) - time()) / (60 * 60 * 24));
                                        if ($daysLeft <= 7): ?>
                                            <div class="text-red-400 font-semibold">
                                                <i class="fas fa-clock mr-1"></i>
                                                <?= $daysLeft ?> days left!
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <!-- Savings Display -->
                                    <div class="bg-salon-gold/10 border border-salon-gold/20 rounded-lg p-4 mb-4">
                                        <div class="text-center">
                                            <div class="text-2xl font-bold text-salon-gold mb-1">
                                                Save <?= $offer['discount'] ?>%
                                            </div>
                                            <div class="text-sm text-gray-300">
                                                Use code: <span class="font-mono font-bold text-salon-gold"><?= $offer['code'] ?></span>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Features -->
                                    <div class="space-y-2 mb-6">
                                        <div class="flex items-center text-sm text-gray-300">
                                            <i class="fas fa-check text-salon-gold mr-2"></i>
                                            Valid for new and existing customers
                                        </div>
                                        <div class="flex items-center text-sm text-gray-300">
                                            <i class="fas fa-check text-salon-gold mr-2"></i>
                                            Can be combined with loyalty points
                                        </div>
                                        <div class="flex items-center text-sm text-gray-300">
                                            <i class="fas fa-check text-salon-gold mr-2"></i>
                                            No hidden fees or charges
                                        </div>
                                    </div>
                                    
                                    <div class="flex gap-3">
                                        <a href="<?= getBasePath() ?>/customer/book" class="flex-1 bg-salon-gold hover:bg-yellow-500 text-black py-3 px-4 rounded-lg font-semibold transition-all duration-300 hover:scale-105 text-center">
                                            Book Now
                                        </a>
                                        <button onclick="copyOfferCode('<?= $offer['code'] ?>')" class="px-4 py-3 border border-salon-gold text-salon-gold rounded-lg hover:bg-salon-gold hover:text-black transition-colors">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </section>

    <!-- Newsletter Signup -->
    <section class="py-20 bg-salon-black">
        <div class="max-w-4xl mx-auto px-6 text-center">
            <div class="bg-gradient-to-r from-salon-gold/10 to-transparent border border-salon-gold/20 rounded-3xl p-12">
                <i class="fas fa-bell text-salon-gold text-4xl mb-6"></i>
                <h2 class="text-3xl md:text-4xl font-bold font-serif text-white mb-6">
                    Never Miss a <span class="text-salon-gold">Deal</span>
                </h2>
                <p class="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
                    Subscribe to our newsletter and be the first to know about exclusive offers, new services, and special promotions.
                </p>
                <form class="max-w-md mx-auto mb-6">
                    <div class="flex flex-col sm:flex-row gap-4">
                        <input 
                            type="email" 
                            placeholder="Enter your email address"
                            class="flex-1 px-6 py-4 bg-secondary-900/80 border border-secondary-700 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold focus:border-transparent"
                            required
                        >
                        <button 
                            type="submit"
                            class="px-8 py-4 bg-salon-gold hover:bg-yellow-500 text-black font-semibold rounded-xl transition-all duration-300 hover:scale-105 whitespace-nowrap"
                        >
                            Subscribe
                        </button>
                    </div>
                </form>
                <p class="text-gray-400 text-sm">
                    Join 5,000+ subscribers and get exclusive deals delivered to your inbox.
                </p>
            </div>
        </div>
    </section>

    <!-- How to Use Offers -->
    <section class="py-20 bg-secondary-900">
        <div class="max-w-7xl mx-auto px-6">
            <div class="text-center mb-16">
                <div class="inline-block bg-salon-gold/10 text-salon-gold px-4 py-2 rounded-full text-sm font-semibold mb-6">
                    How It Works
                </div>
                <h2 class="text-4xl md:text-5xl font-bold font-serif text-white mb-6">
                    How to Use Your <span class="text-salon-gold">Offers</span>
                </h2>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="text-center group">
                    <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-salon-gold/30 transition-colors">
                        <span class="text-salon-gold font-bold text-xl">1</span>
                    </div>
                    <h3 class="text-lg font-bold text-white mb-2">Choose Your Offer</h3>
                    <p class="text-gray-300 text-sm">Browse our current offers and select the one that suits your needs</p>
                </div>

                <div class="text-center group">
                    <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-salon-gold/30 transition-colors">
                        <span class="text-salon-gold font-bold text-xl">2</span>
                    </div>
                    <h3 class="text-lg font-bold text-white mb-2">Book Your Service</h3>
                    <p class="text-gray-300 text-sm">Schedule your appointment online or call us directly</p>
                </div>

                <div class="text-center group">
                    <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-salon-gold/30 transition-colors">
                        <span class="text-salon-gold font-bold text-xl">3</span>
                    </div>
                    <h3 class="text-lg font-bold text-white mb-2">Apply Offer Code</h3>
                    <p class="text-gray-300 text-sm">Enter the offer code during booking or mention it when you arrive</p>
                </div>

                <div class="text-center group">
                    <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-salon-gold/30 transition-colors">
                        <span class="text-salon-gold font-bold text-xl">4</span>
                    </div>
                    <h3 class="text-lg font-bold text-white mb-2">Enjoy & Save</h3>
                    <p class="text-gray-300 text-sm">Relax and enjoy your service while saving money</p>
                </div>
            </div>
        </div>
    </section>

<script>
    function copyOfferCode(code) {
        navigator.clipboard.writeText(code).then(function() {
            // Show success message
            const button = event.target.closest('button');
            const originalHTML = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check"></i>';
            button.classList.add('bg-green-500', 'text-white');

            setTimeout(() => {
                button.innerHTML = originalHTML;
                button.classList.remove('bg-green-500', 'text-white');
            }, 2000);
        }).catch(function(err) {
            console.error('Could not copy text: ', err);
            alert('Offer code: ' + code);
        });
    }
</script>

<?php include __DIR__ . '/includes/footer.php'; ?>
