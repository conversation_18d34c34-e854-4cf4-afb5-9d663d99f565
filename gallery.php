<?php
/**
 * Gallery Page
 * Flix Salon & SPA - PHP Version
 */

require_once __DIR__ . '/config/app.php';

// Get all active gallery images grouped by category
$galleryImages = $database->fetchAll("
    SELECT * FROM gallery
    WHERE is_active = 1
    ORDER BY category ASC, created_at DESC
");

// Group images by category
$imagesByCategory = [];
foreach ($galleryImages as $image) {
    $imagesByCategory[$image['category']][] = $image;
}

$pageTitle = "Gallery";
include __DIR__ . '/includes/header.php';
?>

    <!-- Hero Section -->
    <section class="relative py-24 bg-gradient-to-br from-salon-black via-salon-black to-salon-black">
        <div class="absolute inset-0 bg-gradient-to-r from-salon-gold/5 to-transparent"></div>
        <div class="relative max-w-7xl mx-auto px-6 text-center">
            <div class="inline-block bg-salon-gold/10 text-salon-gold px-4 py-2 rounded-full text-sm font-semibold mb-6">
                Our Work
            </div>
            <h1 class="text-5xl md:text-7xl font-bold font-serif text-white mb-6">
                Beauty <span class="text-salon-gold">Gallery</span>
            </h1>
            <p class="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
                Explore our stunning transformations and see the artistry that makes Flix Salon & SPA the premier destination for beauty services.
            </p>
        </div>
    </section>

    <!-- Filter Tabs -->
    <section class="py-12 bg-secondary-900">
        <div class="max-w-7xl mx-auto px-6">
            <div class="flex flex-wrap justify-center gap-4">
                <button onclick="filterGallery('all')" class="filter-btn active px-6 py-3 bg-salon-gold text-black rounded-lg font-semibold transition-all duration-300 hover:scale-105">
                    All Work
                </button>
                <?php foreach (array_keys($imagesByCategory) as $category): ?>
                    <button onclick="filterGallery('<?= strtolower(str_replace(' ', '-', $category)) ?>')" class="filter-btn px-6 py-3 bg-secondary-900 text-white rounded-lg font-semibold transition-all duration-300 hover:bg-salon-gold hover:text-black border border-secondary-700">
                        <?= htmlspecialchars($category) ?>
                    </button>
                <?php endforeach; ?>
            </div>
        </div>
    </section>

    <!-- Gallery Grid -->
    <section class="py-12 bg-salon-black">
        <div class="max-w-7xl mx-auto px-6">
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6" id="galleryGrid">
                <?php foreach ($imagesByCategory as $category => $images): ?>
                    <?php foreach ($images as $image): ?>
                        <div class="gallery-item group <?= strtolower(str_replace(' ', '-', $category)) ?>" data-category="<?= strtolower(str_replace(' ', '-', $category)) ?>">
                            <div class="relative aspect-square overflow-hidden rounded-xl bg-secondary-900 cursor-pointer" onclick="openLightbox('<?= htmlspecialchars($image['image_url']) ?>', '<?= htmlspecialchars($image['title']) ?>', '<?= htmlspecialchars($image['description']) ?>')">
                                <?php if ($image['image_url']): ?>
                                    <!-- Lazy Loading Placeholder -->
                                    <div class="lazy-placeholder w-full h-full bg-gradient-to-br from-salon-gold/10 to-secondary-900 flex items-center justify-center">
                                        <div class="text-center">
                                            <div class="animate-pulse">
                                                <div class="w-12 h-12 bg-salon-gold/20 rounded-full mx-auto mb-2"></div>
                                                <div class="h-2 bg-salon-gold/20 rounded w-16 mx-auto"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Actual Image (lazy loaded) -->
                                    <img
                                        data-src="<?= htmlspecialchars($image['image_url']) ?>"
                                        alt="<?= htmlspecialchars($image['title']) ?>"
                                        class="lazy-image w-full h-full object-cover group-hover:scale-110 transition-transform duration-500 opacity-0"
                                        loading="lazy"
                                    >
                                <?php else: ?>
                                    <div class="w-full h-full bg-gradient-to-br from-salon-gold/20 to-secondary-900 flex items-center justify-center">
                                        <div class="text-center">
                                            <i class="fas fa-image text-salon-gold/60 text-4xl mb-2"></i>
                                            <div class="text-gray-300 text-sm"><?= htmlspecialchars($category) ?></div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                                
                                <!-- Overlay -->
                                <div class="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                                    <div class="text-center text-white p-4">
                                        <h3 class="text-lg font-semibold mb-2"><?= htmlspecialchars($image['title']) ?></h3>
                                        <p class="text-sm text-gray-300 mb-3"><?= htmlspecialchars($image['description']) ?></p>
                                        <div class="inline-flex items-center bg-salon-gold text-black px-3 py-1 rounded-full text-xs font-semibold">
                                            <?= htmlspecialchars($category) ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endforeach; ?>
            </div>
        </div>
    </section>

    <!-- Call to Action -->
    <section class="py-20 bg-salon-black">
        <div class="max-w-4xl mx-auto px-6 text-center">
            <div class="bg-gradient-to-r from-salon-gold/10 to-transparent border border-salon-gold/20 rounded-3xl p-12">
                <h2 class="text-3xl md:text-4xl font-bold font-serif text-white mb-6">
                    Ready for Your <span class="text-salon-gold">Transformation</span>?
                </h2>
                <p class="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
                    Book your appointment today and let our expert team create your perfect look. Your beauty journey starts here.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="<?= getBasePath() ?>/customer/book" class="bg-salon-gold hover:bg-yellow-500 text-black px-8 py-4 rounded-xl font-semibold transition-all duration-300 hover:scale-105">
                        <i class="fas fa-calendar-plus mr-2"></i>
                        Book Appointment
                    </a>
                    <a href="<?= getBasePath() ?>/contact.php" class="bg-secondary-900 hover:bg-secondary-800 text-white px-8 py-4 rounded-xl font-semibold transition-all duration-300 hover:scale-105 border border-secondary-700">
                        <i class="fas fa-phone mr-2"></i>
                        Contact Us
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Lightbox Modal -->
    <div id="lightboxModal" class="fixed inset-0 bg-black/90 hidden z-50 flex items-center justify-center p-4">
        <div class="relative max-w-4xl w-full">
            <button onclick="closeLightbox()" class="absolute top-4 right-4 text-white hover:text-salon-gold text-2xl z-10">
                <i class="fas fa-times"></i>
            </button>
            
            <div class="bg-secondary-900 rounded-xl overflow-hidden border border-secondary-700">
                <img id="lightboxImage" src="" alt="" class="w-full h-auto max-h-[70vh] object-contain">
                <div class="p-6">
                    <h3 id="lightboxTitle" class="text-xl font-bold text-white mb-2"></h3>
                    <p id="lightboxDescription" class="text-gray-300"></p>
                </div>
            </div>
        </div>
    </div>

<script>
    // Lazy Loading Implementation
    class LazyImageLoader {
        constructor() {
            this.imageObserver = null;
            this.init();
        }

        init() {
            // Check if Intersection Observer is supported
            if ('IntersectionObserver' in window) {
                this.imageObserver = new IntersectionObserver((entries, observer) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            this.loadImage(entry.target);
                            observer.unobserve(entry.target);
                        }
                    });
                }, {
                    rootMargin: '50px 0px', // Start loading 50px before the image enters viewport
                    threshold: 0.01
                });

                this.observeImages();
            } else {
                // Fallback for browsers without Intersection Observer
                this.loadAllImages();
            }
        }

        observeImages() {
            const lazyImages = document.querySelectorAll('.lazy-image');
            lazyImages.forEach(img => {
                this.imageObserver.observe(img);
            });
        }

        loadImage(img) {
            const placeholder = img.previousElementSibling;

            // Create a new image to preload
            const imageLoader = new Image();

            imageLoader.onload = () => {
                // Image loaded successfully
                img.src = img.dataset.src;
                img.classList.remove('opacity-0');
                img.classList.add('opacity-100');

                // Hide placeholder with fade effect
                if (placeholder && placeholder.classList.contains('lazy-placeholder')) {
                    placeholder.style.transition = 'opacity 0.3s ease-out';
                    placeholder.style.opacity = '0';
                    setTimeout(() => {
                        placeholder.style.display = 'none';
                    }, 300);
                }

                // Add loaded class for any additional styling
                img.classList.add('lazy-loaded');
            };

            imageLoader.onerror = () => {
                // Handle image load error
                img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMzMzIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vdCBmb3VuZDwvdGV4dD48L3N2Zz4=';
                img.classList.remove('opacity-0');
                img.classList.add('opacity-100');

                if (placeholder && placeholder.classList.contains('lazy-placeholder')) {
                    placeholder.style.display = 'none';
                }
            };

            // Start loading the image
            imageLoader.src = img.dataset.src;
        }

        loadAllImages() {
            // Fallback: load all images immediately
            const lazyImages = document.querySelectorAll('.lazy-image');
            lazyImages.forEach(img => {
                this.loadImage(img);
            });
        }

        // Method to reinitialize lazy loading after dynamic content changes
        reinitialize() {
            if (this.imageObserver) {
                this.observeImages();
            }
        }
    }

    // Initialize lazy loading
    const lazyLoader = new LazyImageLoader();

    function filterGallery(category) {
        const items = document.querySelectorAll('.gallery-item');
        const buttons = document.querySelectorAll('.filter-btn');

        // Update button states
        buttons.forEach(btn => {
            btn.classList.remove('active', 'bg-salon-gold', 'text-black');
            btn.classList.add('bg-secondary-900', 'text-white');
        });

        event.target.classList.add('active', 'bg-salon-gold', 'text-black');
        event.target.classList.remove('bg-secondary-900', 'text-white');

        // Filter items
        items.forEach(item => {
            if (category === 'all' || item.dataset.category === category) {
                item.style.display = 'block';
                item.style.animation = 'fadeIn 0.5s ease-in-out';
            } else {
                item.style.display = 'none';
            }
        });

        // Reinitialize lazy loading for newly visible images
        setTimeout(() => {
            lazyLoader.reinitialize();
        }, 100);
    }

    function openLightbox(imageUrl, title, description) {
        document.getElementById('lightboxImage').src = imageUrl;
        document.getElementById('lightboxTitle').textContent = title;
        document.getElementById('lightboxDescription').textContent = description;
        document.getElementById('lightboxModal').classList.remove('hidden');
        document.body.style.overflow = 'hidden';
    }

    function closeLightbox() {
        document.getElementById('lightboxModal').classList.add('hidden');
        document.body.style.overflow = 'auto';
    }

    // Close lightbox on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeLightbox();
        }
    });

    // Close lightbox on backdrop click
    document.getElementById('lightboxModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeLightbox();
        }
    });

    // Add animations and lazy loading styles
    const style = document.createElement('style');
    style.textContent = `
        @keyframes fadeIn {
            from { opacity: 0; transform: scale(0.9); }
            to { opacity: 1; transform: scale(1); }
        }

        .lazy-image {
            transition: opacity 0.3s ease-in-out;
        }

        .lazy-placeholder {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }

        .lazy-loaded {
            z-index: 2;
        }

        .gallery-item {
            position: relative;
        }

        /* Pulse animation for loading placeholder */
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .animate-pulse {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
    `;
    document.head.appendChild(style);
</script>

<?php include __DIR__ . '/includes/footer.php'; ?>
