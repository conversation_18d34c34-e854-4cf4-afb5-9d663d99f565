<?php
/**
 * Debug AJAX Reminder System
 * Comprehensive diagnostics to identify and fix issues
 */

require_once 'config/app.php';

echo "<h1>AJAX Reminder System Diagnostics</h1>";

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

$diagnostics = [];
$errors = [];
$warnings = [];

// Test 1: Check if reminder tables exist
echo "<h2>1. Database Tables Check</h2>";
try {
    $reminderTable = $database->fetch("SHOW TABLES LIKE 'booking_reminders'");
    $logTable = $database->fetch("SHOW TABLES LIKE 'reminder_logs'");
    
    if ($reminderTable) {
        echo "✅ booking_reminders table exists<br>";
        
        // Check table structure
        $columns = $database->fetchAll("DESCRIBE booking_reminders");
        $requiredColumns = ['id', 'booking_id', 'reminder_type', 'priority', 'status', 'scheduled_time'];
        $missingColumns = [];
        
        $existingColumns = array_column($columns, 'Field');
        foreach ($requiredColumns as $col) {
            if (!in_array($col, $existingColumns)) {
                $missingColumns[] = $col;
            }
        }
        
        if (empty($missingColumns)) {
            echo "✅ All required columns present<br>";
        } else {
            echo "❌ Missing columns: " . implode(', ', $missingColumns) . "<br>";
            $errors[] = "Missing table columns: " . implode(', ', $missingColumns);
        }
    } else {
        echo "❌ booking_reminders table missing<br>";
        $errors[] = "booking_reminders table does not exist";
    }
    
    if ($logTable) {
        echo "✅ reminder_logs table exists<br>";
    } else {
        echo "⚠️ reminder_logs table missing (will be created)<br>";
        $warnings[] = "reminder_logs table missing";
    }
    
} catch (Exception $e) {
    echo "❌ Database error: " . htmlspecialchars($e->getMessage()) . "<br>";
    $errors[] = "Database connection error: " . $e->getMessage();
}

// Test 2: Check if reminder functions exist
echo "<h2>2. Function Availability Check</h2>";
$requiredFunctions = [
    'createReminderTables',
    'processPendingReminders',
    'checkMissedReminders',
    'scheduleBookingReminders',
    'processReminder'
];

foreach ($requiredFunctions as $func) {
    if (function_exists($func)) {
        echo "✅ $func - Available<br>";
    } else {
        echo "❌ $func - Missing<br>";
        $errors[] = "Function $func is not available";
    }
}

// Test 3: Check AJAX endpoint directly
echo "<h2>3. AJAX Endpoint Test</h2>";
try {
    // Test if the file exists
    if (file_exists('ajax_process_reminders.php')) {
        echo "✅ ajax_process_reminders.php file exists<br>";
        
        // Test direct access
        $url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/ajax_process_reminders.php';
        
        $context = stream_context_create([
            'http' => [
                'method' => 'POST',
                'header' => "Content-Type: application/json\r\nX-Requested-With: XMLHttpRequest",
                'content' => json_encode(['action' => 'test']),
                'timeout' => 10
            ]
        ]);
        
        $result = @file_get_contents($url, false, $context);
        
        if ($result !== false) {
            echo "✅ AJAX endpoint accessible<br>";
            
            $data = json_decode($result, true);
            if ($data) {
                echo "✅ JSON response valid<br>";
                echo "Response: " . htmlspecialchars(substr($result, 0, 200)) . "...<br>";
            } else {
                echo "⚠️ Invalid JSON response<br>";
                echo "Raw response: " . htmlspecialchars(substr($result, 0, 200)) . "...<br>";
                $warnings[] = "AJAX endpoint returns invalid JSON";
            }
        } else {
            echo "❌ AJAX endpoint not accessible<br>";
            $errors[] = "Cannot access AJAX endpoint at $url";
        }
    } else {
        echo "❌ ajax_process_reminders.php file missing<br>";
        $errors[] = "AJAX endpoint file does not exist";
    }
} catch (Exception $e) {
    echo "❌ AJAX endpoint test error: " . htmlspecialchars($e->getMessage()) . "<br>";
    $errors[] = "AJAX endpoint test failed: " . $e->getMessage();
}

// Test 4: Check email system
echo "<h2>4. Email System Check</h2>";
try {
    if (function_exists('sendSMTPEmail')) {
        echo "✅ Email function available<br>";
        
        // Test email configuration
        if (defined('SMTP_HOST') && defined('SMTP_USERNAME')) {
            echo "✅ SMTP configuration defined<br>";
            echo "SMTP Host: " . SMTP_HOST . "<br>";
            echo "SMTP Port: " . SMTP_PORT . "<br>";
            echo "SMTP Username: " . SMTP_USERNAME . "<br>";
        } else {
            echo "❌ SMTP configuration missing<br>";
            $errors[] = "SMTP configuration not properly defined";
        }
    } else {
        echo "❌ Email function not available<br>";
        $errors[] = "sendSMTPEmail function not available";
    }
} catch (Exception $e) {
    echo "❌ Email system error: " . htmlspecialchars($e->getMessage()) . "<br>";
    $errors[] = "Email system error: " . $e->getMessage();
}

// Test 5: Check for sample data
echo "<h2>5. Sample Data Check</h2>";
try {
    $bookingCount = $database->fetch("SELECT COUNT(*) as count FROM bookings WHERE status IN ('CONFIRMED', 'PENDING') AND date >= CURDATE()")['count'];
    echo "Active bookings: $bookingCount<br>";
    
    if ($bookingCount > 0) {
        echo "✅ Sample bookings available for testing<br>";
        
        // Check if any reminders exist
        if ($reminderTable) {
            $reminderCount = $database->fetch("SELECT COUNT(*) as count FROM booking_reminders")['count'];
            echo "Existing reminders: $reminderCount<br>";
            
            if ($reminderCount > 0) {
                $pendingCount = $database->fetch("SELECT COUNT(*) as count FROM booking_reminders WHERE status = 'PENDING'")['count'];
                echo "Pending reminders: $pendingCount<br>";
            }
        }
    } else {
        echo "⚠️ No active bookings for testing<br>";
        $warnings[] = "No active bookings available for reminder testing";
    }
} catch (Exception $e) {
    echo "❌ Sample data check error: " . htmlspecialchars($e->getMessage()) . "<br>";
    $errors[] = "Sample data check failed: " . $e->getMessage();
}

// Test 6: Manual reminder processing test
echo "<h2>6. Manual Processing Test</h2>";
try {
    if (empty($errors)) {
        echo "Attempting manual reminder processing...<br>";
        
        // Initialize tables if needed
        createReminderTables();
        
        // Try processing
        $results = processPendingReminders();
        echo "✅ Manual processing successful<br>";
        echo "Processed: " . $results['processed'] . "<br>";
        echo "Sent: " . $results['sent'] . "<br>";
        echo "Failed: " . $results['failed'] . "<br>";
        echo "Skipped: " . $results['skipped'] . "<br>";
        
        // Try missed reminders check
        $missedResults = checkMissedReminders();
        echo "Missed reminders check - Found: " . $missedResults['missed_found'] . "<br>";
        
    } else {
        echo "⚠️ Skipping manual test due to previous errors<br>";
    }
} catch (Exception $e) {
    echo "❌ Manual processing error: " . htmlspecialchars($e->getMessage()) . "<br>";
    $errors[] = "Manual processing failed: " . $e->getMessage();
}

// Test 7: JavaScript file check
echo "<h2>7. JavaScript File Check</h2>";
if (file_exists('assets/js/reminder-processor.js')) {
    echo "✅ reminder-processor.js file exists<br>";
    $jsSize = filesize('assets/js/reminder-processor.js');
    echo "File size: " . number_format($jsSize) . " bytes<br>";
    
    if ($jsSize > 1000) {
        echo "✅ JavaScript file appears complete<br>";
    } else {
        echo "⚠️ JavaScript file seems too small<br>";
        $warnings[] = "JavaScript file may be incomplete";
    }
} else {
    echo "❌ reminder-processor.js file missing<br>";
    $errors[] = "JavaScript processor file does not exist";
}

// Summary and recommendations
echo "<h2>8. Summary and Recommendations</h2>";

if (empty($errors)) {
    echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>✅ System Status: HEALTHY</h3>";
    echo "<p>No critical errors found. The AJAX reminder system should be working correctly.</p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>❌ Critical Errors Found</h3>";
    echo "<ul>";
    foreach ($errors as $error) {
        echo "<li>" . htmlspecialchars($error) . "</li>";
    }
    echo "</ul>";
    echo "</div>";
}

if (!empty($warnings)) {
    echo "<div style='background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>⚠️ Warnings</h3>";
    echo "<ul>";
    foreach ($warnings as $warning) {
        echo "<li>" . htmlspecialchars($warning) . "</li>";
    }
    echo "</ul>";
    echo "</div>";
}

// Automatic fixes
echo "<h2>9. Automatic Fixes</h2>";

if (in_array("booking_reminders table does not exist", $errors)) {
    echo "Attempting to create missing tables...<br>";
    try {
        createReminderTables();
        echo "✅ Tables created successfully<br>";
    } catch (Exception $e) {
        echo "❌ Failed to create tables: " . htmlspecialchars($e->getMessage()) . "<br>";
    }
}

// Quick fix recommendations
echo "<h2>10. Quick Fix Recommendations</h2>";
echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px;'>";
echo "<h3>🔧 Immediate Actions:</h3>";
echo "<ol>";

if (!empty($errors)) {
    echo "<li><strong>Fix Critical Errors:</strong> Address the errors listed above first</li>";
}

echo "<li><strong>Initialize System:</strong> Run <a href='create_reminder_tables.php'>create_reminder_tables.php</a></li>";
echo "<li><strong>Test AJAX Endpoint:</strong> Access <a href='ajax_process_reminders.php'>ajax_process_reminders.php</a> directly</li>";
echo "<li><strong>Create Test Data:</strong> Use <a href='test_ajax_reminders.php'>test_ajax_reminders.php</a> to create test bookings</li>";
echo "<li><strong>Check Browser Console:</strong> Look for JavaScript errors in browser developer tools</li>";
echo "<li><strong>Enable Debug Mode:</strong> Set debug: true in JavaScript processor options</li>";
echo "</ol>";
echo "</div>";

// Debug information
echo "<h2>11. Debug Information</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 12px;'>";
echo "<strong>PHP Version:</strong> " . phpversion() . "<br>";
echo "<strong>Server:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "<br>";
echo "<strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "<strong>Current Directory:</strong> " . __DIR__ . "<br>";
echo "<strong>Include Path:</strong> " . get_include_path() . "<br>";
echo "<strong>Memory Limit:</strong> " . ini_get('memory_limit') . "<br>";
echo "<strong>Max Execution Time:</strong> " . ini_get('max_execution_time') . "<br>";
echo "<strong>Error Reporting:</strong> " . error_reporting() . "<br>";
echo "</div>";

echo "<h2>12. Next Steps</h2>";
echo "<p>Based on the diagnostics above:</p>";
echo "<ol>";
echo "<li>Fix any critical errors identified</li>";
echo "<li>Run the table creation script if needed</li>";
echo "<li>Test the AJAX endpoint directly</li>";
echo "<li>Check browser console for JavaScript errors</li>";
echo "<li>Create test bookings and verify reminder processing</li>";
echo "</ol>";
?>
