<?php
/**
 * Debug DPO API Connection
 * Tests the DPO API connection and shows raw responses
 */

require_once __DIR__ . '/config/app.php';

echo "<h1>DPO API Debug</h1>";

// Test 1: Configuration Check
echo "<h2>1. Configuration Check</h2>";
echo "<p><strong>DPO_ENABLED:</strong> " . (DPO_ENABLED ? 'Yes' : 'No') . "</p>";
echo "<p><strong>DPO_COMPANY_TOKEN:</strong> " . DPO_COMPANY_TOKEN . "</p>";
echo "<p><strong>DPO_SERVICE_TYPE:</strong> " . DPO_SERVICE_TYPE . "</p>";
echo "<p><strong>DPO_TEST_MODE:</strong> " . (DPO_TEST_MODE ? 'Yes' : 'No') . "</p>";
echo "<p><strong>DPO_API_URL:</strong> " . DPO_API_URL . "</p>";

// Test 2: Check if credentials are configured
echo "<h2>2. Credentials Check</h2>";
if (DPO_COMPANY_TOKEN === 'YOUR_DPO_COMPANY_TOKEN_HERE') {
    echo "<p style='color: red;'>❌ DPO_COMPANY_TOKEN is not configured</p>";
    echo "<p>Please update your DPO credentials in config/app.php</p>";
    echo "<p>You can get these from your DPO Pay portal at https://portal.dpopay.com/</p>";
    exit;
} else {
    echo "<p style='color: green;'>✅ DPO_COMPANY_TOKEN is configured</p>";
}

if (DPO_SERVICE_TYPE === 'YOUR_DPO_SERVICE_TYPE_HERE') {
    echo "<p style='color: red;'>❌ DPO_SERVICE_TYPE is not configured</p>";
    echo "<p>Please update your DPO credentials in config/app.php</p>";
    exit;
} else {
    echo "<p style='color: green;'>✅ DPO_SERVICE_TYPE is configured</p>";
}

// Test 3: Test API connectivity
echo "<h2>3. API Connectivity Test</h2>";

$testXml = <<<XML
<?xml version="1.0" encoding="utf-8"?>
<API3G>
    <CompanyToken>INVALID_TOKEN_FOR_TEST</CompanyToken>
    <Request>createToken</Request>
    <Transaction>
        <PaymentAmount>100</PaymentAmount>
        <PaymentCurrency>TZS</PaymentCurrency>
        <CompanyRef>TEST_REF</CompanyRef>
        <RedirectURL>http://example.com/return</RedirectURL>
        <BackURL>http://example.com/back</BackURL>
        <CompanyRefUnique>0</CompanyRefUnique>
        <PTL>5</PTL>
    </Transaction>
    <Services>
        <Service>
            <ServiceType>TEST</ServiceType>
            <ServiceDescription>Test Payment</ServiceDescription>
            <ServiceDate>2024-01-01 12:00</ServiceDate>
        </Service>
    </Services>
    <Customer>
        <CustomerFirstName>Test</CustomerFirstName>
        <CustomerLastName>Customer</CustomerLastName>
        <CustomerAddress>Test Address</CustomerAddress>
        <CustomerCity>Test City</CustomerCity>
        <CustomerPhone>+255123456789</CustomerPhone>
        <CustomerEmail><EMAIL></CustomerEmail>
    </Customer>
</API3G>
XML;

echo "<h3>Testing with invalid token (to check API response format):</h3>";
echo "<p><strong>API URL:</strong> " . DPO_API_URL . "/API/v6/</p>";

$curl = curl_init();
curl_setopt_array($curl, [
    CURLOPT_URL => DPO_API_URL . "/API/v6/",
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_ENCODING => "",
    CURLOPT_MAXREDIRS => 10,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    CURLOPT_CUSTOMREQUEST => "POST",
    CURLOPT_POSTFIELDS => $testXml,
    CURLOPT_HTTPHEADER => [
        "cache-control: no-cache",
        "Content-Type: application/xml",
    ],
]);

$response = curl_exec($curl);
$httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
$error = curl_error($curl);
curl_close($curl);

echo "<p><strong>HTTP Code:</strong> $httpCode</p>";

if ($error) {
    echo "<p style='color: red;'><strong>cURL Error:</strong> $error</p>";
} else {
    echo "<p style='color: green;'>✅ API connection successful</p>";
}

echo "<h3>Raw Response:</h3>";
echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd; max-height: 400px; overflow-y: auto;'>";
echo htmlspecialchars($response);
echo "</pre>";

// Test 4: Analyze response format
echo "<h2>4. Response Analysis</h2>";

if (empty($response)) {
    echo "<p style='color: red;'>❌ Empty response from API</p>";
} else {
    echo "<p><strong>Response Length:</strong> " . strlen($response) . " characters</p>";
    
    if (stripos($response, '<html') !== false) {
        echo "<p style='color: red;'>❌ Response is HTML (likely an error page)</p>";
        echo "<p>This usually means:</p>";
        echo "<ul>";
        echo "<li>Invalid API URL</li>";
        echo "<li>API endpoint not found</li>";
        echo "<li>Server configuration issue</li>";
        echo "</ul>";
    } elseif (stripos($response, '<?xml') !== false || stripos($response, '<API3G>') !== false) {
        echo "<p style='color: green;'>✅ Response is XML format</p>";
        
        try {
            $xml = new SimpleXMLElement($response);
            echo "<p style='color: green;'>✅ XML is valid and parseable</p>";
            
            if ($xml->xpath('Result')) {
                $result = $xml->xpath('Result')[0]->__toString();
                echo "<p><strong>Result Code:</strong> $result</p>";
            }
            
            if ($xml->xpath('ResultExplanation')) {
                $explanation = $xml->xpath('ResultExplanation')[0]->__toString();
                echo "<p><strong>Result Explanation:</strong> $explanation</p>";
            }
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ XML parsing failed: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠ Response format is unknown</p>";
        echo "<p>First 200 characters:</p>";
        echo "<pre>" . htmlspecialchars(substr($response, 0, 200)) . "</pre>";
    }
}

// Test 5: Test with actual credentials
echo "<h2>5. Test with Actual Credentials</h2>";

$actualTestXml = <<<XML
<?xml version="1.0" encoding="utf-8"?>
<API3G>
    <CompanyToken>{DPO_COMPANY_TOKEN}</CompanyToken>
    <Request>createToken</Request>
    <Transaction>
        <PaymentAmount>1000</PaymentAmount>
        <PaymentCurrency>TZS</PaymentCurrency>
        <CompanyRef>DEBUG_TEST_{time()}</CompanyRef>
        <RedirectURL>http://localhost/flix-php/test-return.php</RedirectURL>
        <BackURL>http://localhost/flix-php/test-back.php</BackURL>
        <CompanyRefUnique>0</CompanyRefUnique>
        <PTL>5</PTL>
    </Transaction>
    <Services>
        <Service>
            <ServiceType>{DPO_SERVICE_TYPE}</ServiceType>
            <ServiceDescription>Debug Test Payment</ServiceDescription>
            <ServiceDate>{date('Y/m/d H:i')}</ServiceDate>
        </Service>
    </Services>
    <Customer>
        <CustomerFirstName>Debug</CustomerFirstName>
        <CustomerLastName>Test</CustomerLastName>
        <CustomerAddress>Test Address</CustomerAddress>
        <CustomerCity>Dar es Salaam</CustomerCity>
        <CustomerPhone>+255123456789</CustomerPhone>
        <CustomerEmail><EMAIL></CustomerEmail>
    </Customer>
</API3G>
XML;

// Replace placeholders
$actualTestXml = str_replace('{DPO_COMPANY_TOKEN}', DPO_COMPANY_TOKEN, $actualTestXml);
$actualTestXml = str_replace('{DPO_SERVICE_TYPE}', DPO_SERVICE_TYPE, $actualTestXml);
$actualTestXml = str_replace('{time()}', time(), $actualTestXml);
$actualTestXml = str_replace('{date(\'Y/m/d H:i\')}', date('Y/m/d H:i'), $actualTestXml);

echo "<h3>Testing with your actual credentials:</h3>";
echo "<p><em>Note: This will attempt to create a real test token</em></p>";

$curl2 = curl_init();
curl_setopt_array($curl2, [
    CURLOPT_URL => DPO_API_URL . "/API/v6/",
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_ENCODING => "",
    CURLOPT_MAXREDIRS => 10,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    CURLOPT_CUSTOMREQUEST => "POST",
    CURLOPT_POSTFIELDS => $actualTestXml,
    CURLOPT_HTTPHEADER => [
        "cache-control: no-cache",
        "Content-Type: application/xml",
    ],
]);

$response2 = curl_exec($curl2);
$httpCode2 = curl_getinfo($curl2, CURLINFO_HTTP_CODE);
$error2 = curl_error($curl2);
curl_close($curl2);

echo "<p><strong>HTTP Code:</strong> $httpCode2</p>";

if ($error2) {
    echo "<p style='color: red;'><strong>cURL Error:</strong> $error2</p>";
} else {
    echo "<p style='color: green;'>✅ API call completed</p>";
}

echo "<h3>Response with Actual Credentials:</h3>";
echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd; max-height: 400px; overflow-y: auto;'>";
echo htmlspecialchars($response2);
echo "</pre>";

// Analyze the actual response
if (!empty($response2)) {
    if (stripos($response2, '<html') !== false) {
        echo "<p style='color: red;'>❌ Still getting HTML response with actual credentials</p>";
        echo "<p>This suggests an issue with the API URL or DPO service configuration.</p>";
    } elseif (stripos($response2, '<?xml') !== false || stripos($response2, '<API3G>') !== false) {
        echo "<p style='color: green;'>✅ Getting XML response with actual credentials</p>";
        
        try {
            $xml2 = new SimpleXMLElement($response2);
            
            if ($xml2->xpath('Result')) {
                $result2 = $xml2->xpath('Result')[0]->__toString();
                echo "<p><strong>Result Code:</strong> $result2</p>";
                
                if ($result2 === '000') {
                    echo "<p style='color: green;'>✅ Token creation successful!</p>";
                    if ($xml2->xpath('TransToken')) {
                        $token = $xml2->xpath('TransToken')[0]->__toString();
                        echo "<p><strong>Token:</strong> $token</p>";
                    }
                } else {
                    echo "<p style='color: orange;'>⚠ Token creation failed with code: $result2</p>";
                }
            }
            
            if ($xml2->xpath('ResultExplanation')) {
                $explanation2 = $xml2->xpath('ResultExplanation')[0]->__toString();
                echo "<p><strong>Explanation:</strong> $explanation2</p>";
            }
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ XML parsing failed: " . $e->getMessage() . "</p>";
        }
    }
}

echo "<h2>Summary</h2>";
echo "<p>Use this information to troubleshoot your DPO integration:</p>";
echo "<ul>";
echo "<li>If you're getting HTML responses, check your API URL and credentials</li>";
echo "<li>If you're getting XML with error codes, check your DPO account configuration</li>";
echo "<li>If everything looks good, the integration should work properly</li>";
echo "</ul>";
?>
