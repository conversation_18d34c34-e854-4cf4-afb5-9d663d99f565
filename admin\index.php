<?php
/**
 * Admin Dashboard
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../config/app.php';

// Require admin authentication
$auth->requireRole('ADMIN');

// Get dashboard statistics
$stats = [];

// Total customers
$stats['customers'] = $database->fetch("SELECT COUNT(*) as count FROM users WHERE role = 'CUSTOMER'")['count'];

// Total bookings
$stats['bookings'] = $database->fetch("SELECT COUNT(*) as count FROM bookings")['count'];

// Total revenue
$stats['revenue'] = $database->fetch("SELECT SUM(total_amount) as total FROM bookings WHERE status = 'COMPLETED'")['total'] ?? 0;

// Pending bookings
$stats['pending_bookings'] = $database->fetch("SELECT COUNT(*) as count FROM bookings WHERE status = 'PENDING'")['count'];

// Recent bookings
$recentBookings = $database->fetchAll("
    SELECT b.*, u.name as customer_name, s.name as service_name, st.name as staff_name
    FROM bookings b
    LEFT JOIN users u ON b.user_id = u.id
    LEFT JOIN services s ON b.service_id = s.id
    LEFT JOIN users st ON b.staff_id = st.id AND st.role = 'STAFF'
    GROUP BY b.id
    ORDER BY b.created_at DESC
    LIMIT 10
");

// Monthly revenue data for chart
$monthlyRevenue = $database->fetchAll("
    SELECT 
        DATE_FORMAT(date, '%Y-%m') as month,
        SUM(total_amount) as revenue
    FROM bookings 
    WHERE status = 'COMPLETED' 
    AND date >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
    GROUP BY DATE_FORMAT(date, '%Y-%m')
    ORDER BY month
");

$pageTitle = "Admin Dashboard";
include __DIR__ . '/../includes/admin_header.php';
?>

<div class="min-h-screen bg-salon-black pt-20">
    <!-- Page Header
    <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 rounded-2xl shadow-xl mb-8 mx-4 sm:mx-6 lg:mx-8 hover-lift">
        <div class="px-6 sm:px-8 lg:px-10">
            <div class="py-8 md:flex md:items-center md:justify-between">
                <div class="flex-1 min-w-0">
                    <div class="flex items-center">
                        <div>
                            <div class="flex items-center">
                                <h1 class="text-3xl font-bold leading-7 text-white sm:leading-9 sm:truncate font-serif">
                                    Admin <span class="text-salon-gold">Dashboard</span>
                                </h1>
                            </div>
                            <dl class="mt-3 flex flex-col sm:mt-2 sm:flex-row sm:flex-wrap">
                                <dt class="sr-only">Account status</dt>
                                <dd class="flex items-center text-lg text-gray-300 font-medium capitalize sm:mr-6">
                                    <svg class="flex-shrink-0 mr-2 h-5 w-5 text-salon-gold" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                    </svg>
                                    System Active
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>   -->

    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                <!-- Stats Overview -->
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-8">
                    <!-- Total Customers -->
                    <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 overflow-hidden shadow-xl rounded-2xl hover-lift">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 bg-salon-gold/20 rounded-full flex items-center justify-center">
                                        <svg class="h-6 w-6 text-salon-gold" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-400 truncate">Total Customers</dt>
                                        <dd class="text-2xl font-bold text-salon-gold"><?= number_format($stats['customers']) ?></dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Total Bookings -->
                    <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 overflow-hidden shadow-xl rounded-2xl hover-lift">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 bg-salon-gold/20 rounded-full flex items-center justify-center">
                                        <svg class="h-6 w-6 text-salon-gold" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-400 truncate">Total Bookings</dt>
                                        <dd class="text-2xl font-bold text-white"><?= number_format($stats['bookings']) ?></dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Total Revenue -->
                    <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 overflow-hidden shadow-xl rounded-2xl hover-lift">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 bg-salon-gold/20 rounded-full flex items-center justify-center">
                                        <svg class="h-6 w-6 text-salon-gold" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-400 truncate">Total Revenue</dt>
                                        <dd class="text-2xl font-bold text-salon-gold"><?= formatCurrency($stats['revenue']) ?></dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Pending Bookings -->
                    <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 overflow-hidden shadow-xl rounded-2xl hover-lift">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 bg-salon-gold/20 rounded-full flex items-center justify-center">
                                        <svg class="h-6 w-6 text-salon-gold" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-400 truncate">Pending Bookings</dt>
                                        <dd class="text-2xl font-bold text-white"><?= number_format($stats['pending_bookings']) ?></dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Bookings -->
                <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 shadow-xl overflow-hidden rounded-2xl mb-8 hover-lift">
                    <div class="px-6 py-6 sm:px-8">
                        <h3 class="text-xl leading-6 font-bold text-white">Recent Bookings</h3>
                        <p class="mt-2 max-w-2xl text-sm text-gray-300">Latest booking requests and appointments</p>
                    </div>
                    <ul class="divide-y divide-secondary-700">
                        <?php foreach ($recentBookings as $booking): ?>
                            <li class="hover:bg-secondary-800/50 transition-colors duration-200">
                                <div class="px-6 py-5 sm:px-8">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-12 w-12">
                                                <div class="h-12 w-12 rounded-full bg-salon-gold flex items-center justify-center">
                                                    <span class="text-sm font-semibold text-black">
                                                        <?= strtoupper(substr($booking['customer_name'], 0, 2)) ?>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="ml-5">
                                                <div class="text-sm font-semibold text-white">
                                                    <?= htmlspecialchars($booking['customer_name']) ?>
                                                </div>
                                                <div class="text-sm text-gray-300">
                                                    <?= htmlspecialchars($booking['service_name'] ?? 'Package Booking') ?>
                                                    <?php if ($booking['staff_name']): ?>
                                                        with <?= htmlspecialchars($booking['staff_name']) ?>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-6">
                                            <div class="text-right">
                                                <div class="text-sm font-medium text-white">
                                                    <?= formatDate($booking['date']) ?> at <?= formatTime($booking['start_time']) ?>
                                                </div>
                                                <div class="text-sm font-semibold text-salon-gold">
                                                    <?= formatCurrency($booking['total_amount']) ?>
                                                </div>
                                            </div>
                                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold <?= getStatusBadgeClass($booking['status']) ?>">
                                                <?= ucfirst(strtolower($booking['status'])) ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                    <div class="bg-secondary-800/50 px-6 py-4 sm:px-8">
                        <div class="flex justify-between">
                            <div></div>
                            <a href="/admin/bookings" class="text-sm font-semibold text-salon-gold hover:text-gold-light transition-colors duration-200">
                                View all bookings →
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                    <a href="/admin/bookings" class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 p-8 rounded-2xl hover:bg-secondary-800/50 hover:border-salon-gold/50 transition-all duration-300 group hover-lift">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-12 h-12 bg-salon-gold/20 rounded-full flex items-center justify-center group-hover:bg-salon-gold/30 transition-colors duration-300">
                                    <svg class="h-6 w-6 text-salon-gold group-hover:text-gold-light" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-lg font-semibold text-white group-hover:text-salon-gold transition-colors duration-300">Manage Bookings</h3>
                                <p class="text-sm text-gray-300 mt-1">View and manage all appointments</p>
                            </div>
                        </div>
                    </a>

                    <a href="/admin/services" class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 p-8 rounded-2xl hover:bg-secondary-800/50 hover:border-salon-gold/50 transition-all duration-300 group hover-lift">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-12 h-12 bg-salon-gold/20 rounded-full flex items-center justify-center group-hover:bg-salon-gold/30 transition-colors duration-300">
                                    <svg class="h-6 w-6 text-salon-gold group-hover:text-gold-light" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17v4a2 2 0 002 2h4M13 13h4a2 2 0 012 2v4a2 2 0 01-2 2h-4m-6-4a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2z" />
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-lg font-semibold text-white group-hover:text-salon-gold transition-colors duration-300">Manage Services</h3>
                                <p class="text-sm text-gray-300 mt-1">Add and edit salon services</p>
                            </div>
                        </div>
                    </a>

                    <a href="/admin/customers" class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 p-8 rounded-2xl hover:bg-secondary-800/50 hover:border-salon-gold/50 transition-all duration-300 group hover-lift">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-12 h-12 bg-salon-gold/20 rounded-full flex items-center justify-center group-hover:bg-salon-gold/30 transition-colors duration-300">
                                    <svg class="h-6 w-6 text-salon-gold group-hover:text-gold-light" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-lg font-semibold text-white group-hover:text-salon-gold transition-colors duration-300">Manage Customers</h3>
                                <p class="text-sm text-gray-300 mt-1">View customer information</p>
                            </div>
                        </div>
                    </a>
                </div>
                </div>
            </main>
        </div>
    </div>
</div>

<?php include __DIR__ . '/../includes/admin_footer.php'; ?>
