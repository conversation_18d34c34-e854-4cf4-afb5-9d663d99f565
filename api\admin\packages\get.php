<?php
session_start();
require_once __DIR__ . '/../../../config/app.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit();
}

// Get package ID from query parameter or URL path
$packageId = $_GET['id'] ?? '';

// If not in query parameter, try to get from URL path
if (empty($packageId)) {
    $requestUri = $_SERVER['REQUEST_URI'];
    $pathParts = explode('/', trim(parse_url($requestUri, PHP_URL_PATH), '/'));
    $packageId = end($pathParts);
}

// Check if package ID is provided
if (empty($packageId)) {
    http_response_code(400);
    echo json_encode(['error' => 'Package ID is required']);
    exit();
}

try {
    $package = getPackageById($packageId);

    if (!$package) {
        http_response_code(404);
        echo json_encode(['error' => 'Package not found']);
        exit();
    }

    // Get service IDs for the package
    $packageServices = getPackageServices($packageId);
    $serviceIds = array_column($packageServices, 'id');

    // Add service IDs to package data for form population
    $package['services'] = $serviceIds;

    // Return package data
    header('Content-Type: application/json');
    echo json_encode($package);
    
} catch (Exception $e) {
    error_log("Get package API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}
?>
