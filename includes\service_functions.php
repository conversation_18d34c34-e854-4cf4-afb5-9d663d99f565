<?php
/**
 * Service Management Functions
 * Flix Salonce - PHP Version
 */

/**
 * Create a new service
 */
function createService($data) {
    global $database;
    
    try {
        // Validate required fields
        if (empty($data['name']) || empty($data['price']) || empty($data['duration'])) {
            return ['success' => false, 'error' => 'Name, price, and duration are required'];
        }
        
        // Handle image (either upload or URL)
        $imagePath = null;

        // Check if image URL is provided
        if (!empty($data['image_url'])) {
            // Validate URL
            if (filter_var($data['image_url'], FILTER_VALIDATE_URL)) {
                $imagePath = $data['image_url'];
            } else {
                return ['success' => false, 'error' => 'Invalid image URL provided'];
            }
        }
        // Check if file upload is provided
        elseif (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $uploadResult = handleImageUpload($_FILES['image'], 'services');
            if ($uploadResult['success']) {
                $imagePath = $uploadResult['path'];
            } else {
                return ['success' => false, 'error' => $uploadResult['error']];
            }
        }
        
        $serviceId = generateUUID();
        
        $database->query(
            "INSERT INTO services (id, name, description, price, duration, category, image, is_active, created_at, updated_at) 
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())",
            [
                $serviceId,
                sanitize($data['name']),
                sanitize($data['description'] ?? ''),
                floatval($data['price']),
                intval($data['duration']),
                sanitize($data['category'] ?? null),
                $imagePath,
                isset($data['is_active']) ? 1 : 0
            ]
        );
        
        return ['success' => true, 'id' => $serviceId];
        
    } catch (Exception $e) {
        error_log("Service creation error: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to create service'];
    }
}

/**
 * Update an existing service
 */
function updateService($serviceId, $data) {
    global $database;
    
    try {
        // Validate required fields
        if (empty($data['name']) || empty($data['price']) || empty($data['duration'])) {
            return ['success' => false, 'error' => 'Name, price, and duration are required'];
        }
        
        // Get current service data
        $currentService = $database->fetch("SELECT * FROM services WHERE id = ?", [$serviceId]);
        if (!$currentService) {
            return ['success' => false, 'error' => 'Service not found'];
        }
        
        // Handle image (either upload or URL)
        $imagePath = $currentService['image'];

        // Check if image URL is provided
        if (!empty($data['image_url'])) {
            // Validate URL
            if (filter_var($data['image_url'], FILTER_VALIDATE_URL)) {
                // Delete old uploaded image if it exists and is not a URL
                if ($imagePath && !filter_var($imagePath, FILTER_VALIDATE_URL) && file_exists(UPLOAD_PATH . $imagePath)) {
                    unlink(UPLOAD_PATH . $imagePath);
                }
                $imagePath = $data['image_url'];
            } else {
                return ['success' => false, 'error' => 'Invalid image URL provided'];
            }
        }
        // Check if file upload is provided
        elseif (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $uploadResult = handleImageUpload($_FILES['image'], 'services');
            if ($uploadResult['success']) {
                // Delete old uploaded image if it exists and is not a URL
                if ($imagePath && !filter_var($imagePath, FILTER_VALIDATE_URL) && file_exists(UPLOAD_PATH . $imagePath)) {
                    unlink(UPLOAD_PATH . $imagePath);
                }
                $imagePath = $uploadResult['path'];
            } else {
                return ['success' => false, 'error' => $uploadResult['error']];
            }
        }
        
        $database->query(
            "UPDATE services SET name = ?, description = ?, price = ?, duration = ?, category = ?, image = ?, is_active = ?, updated_at = NOW() 
             WHERE id = ?",
            [
                sanitize($data['name']),
                sanitize($data['description'] ?? ''),
                floatval($data['price']),
                intval($data['duration']),
                sanitize($data['category'] ?? null),
                $imagePath,
                isset($data['is_active']) ? 1 : 0,
                $serviceId
            ]
        );
        
        return ['success' => true];
        
    } catch (Exception $e) {
        error_log("Service update error: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to update service'];
    }
}

/**
 * Delete a service
 */
function deleteService($serviceId) {
    global $database;
    
    try {
        // Check if service exists
        $service = $database->fetch("SELECT * FROM services WHERE id = ?", [$serviceId]);
        if (!$service) {
            return ['success' => false, 'error' => 'Service not found'];
        }

        // Check if service has active bookings
        $activeBookings = $database->fetchAll(
            "SELECT b.id, b.date, b.start_time, b.status, u.name as customer_name
             FROM bookings b
             JOIN users u ON b.user_id = u.id
             WHERE b.service_id = ? AND b.status IN ('PENDING', 'CONFIRMED')",
            [$serviceId]
        );

        if (count($activeBookings) > 0) {
            $bookingDetails = [];
            foreach ($activeBookings as $booking) {
                $bookingDetails[] = "• {$booking['customer_name']} on {$booking['date']} at {$booking['start_time']} ({$booking['status']})";
            }
            return [
                'success' => false,
                'error' => 'Cannot delete service with active bookings',
                'details' => 'The following bookings must be cancelled or completed first:',
                'bookings' => $bookingDetails
            ];
        }

        // Check if service has completed bookings (offer alternative)
        $completedBookings = $database->fetch(
            "SELECT COUNT(*) as count FROM bookings WHERE service_id = ? AND status = 'COMPLETED'",
            [$serviceId]
        )['count'];

        if ($completedBookings > 0) {
            return [
                'success' => false,
                'error' => 'Service has booking history',
                'details' => "This service has {$completedBookings} completed booking(s). Instead of deleting, consider:",
                'suggestions' => [
                    'Mark the service as inactive to hide it from new bookings',
                    'Archive the service for historical records',
                    'Contact administrator for data cleanup'
                ]
            ];
        }

        // Check if service is part of any packages
        $packageCount = $database->fetch(
            "SELECT COUNT(*) as count FROM package_services WHERE service_id = ?",
            [$serviceId]
        )['count'];

        if ($packageCount > 0) {
            // Get package names for better error message
            $packages = $database->fetchAll(
                "SELECT p.name FROM packages p
                 JOIN package_services ps ON p.id = ps.package_id
                 WHERE ps.service_id = ?",
                [$serviceId]
            );
            $packageNames = array_column($packages, 'name');
            return ['success' => false, 'error' => 'Cannot delete service that is part of packages: ' . implode(', ', $packageNames)];
        }

        // Delete the service
        $result = $database->query("DELETE FROM services WHERE id = ?", [$serviceId]);

        if ($result === false) {
            return ['success' => false, 'error' => 'Failed to delete service from database'];
        }

        // Delete associated image (only if it's an uploaded file, not a URL)
        if ($service['image'] && !filter_var($service['image'], FILTER_VALIDATE_URL) && file_exists(UPLOAD_PATH . $service['image'])) {
            unlink(UPLOAD_PATH . $service['image']);
        }

        return ['success' => true];
        
    } catch (Exception $e) {
        error_log("Service deletion error: " . $e->getMessage());
        return [
            'success' => false,
            'error' => 'Database error occurred',
            'details' => 'An unexpected error occurred while deleting the service. Please try again or contact support.',
            'technical_error' => $e->getMessage()
        ];
    }
}

/**
 * Get service by ID
 */
function getServiceById($serviceId) {
    global $database;
    
    return $database->fetch("SELECT * FROM services WHERE id = ?", [$serviceId]);
}

/**
 * Get all active services
 */
function getActiveServices() {
    global $database;
    
    return $database->fetchAll("SELECT * FROM services WHERE is_active = 1 ORDER BY name");
}

/**
 * Get services by category
 */
function getServicesByCategory($category) {
    global $database;
    
    return $database->fetchAll(
        "SELECT * FROM services WHERE category = ? AND is_active = 1 ORDER BY name",
        [$category]
    );
}

/**
 * Handle image upload for services
 */
function handleImageUpload($file, $folder = 'services') {
    try {
        // Validate file
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!in_array($file['type'], $allowedTypes)) {
            return ['success' => false, 'error' => 'Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed.'];
        }
        
        // Check file size (max 5MB)
        if ($file['size'] > 5 * 1024 * 1024) {
            return ['success' => false, 'error' => 'File size too large. Maximum 5MB allowed.'];
        }
        
        // Create upload directory if it doesn't exist
        $uploadDir = UPLOAD_PATH . $folder . '/';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        // Generate unique filename
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = generateUUID() . '.' . $extension;
        $filepath = $uploadDir . $filename;
        
        // Move uploaded file
        if (move_uploaded_file($file['tmp_name'], $filepath)) {
            // Resize image if needed
            resizeImage($filepath, 800, 600);
            
            return ['success' => true, 'path' => $folder . '/' . $filename];
        } else {
            return ['success' => false, 'error' => 'Failed to upload file'];
        }
        
    } catch (Exception $e) {
        error_log("Image upload error: " . $e->getMessage());
        return ['success' => false, 'error' => 'Upload failed'];
    }
}

/**
 * Resize image to fit within specified dimensions
 */
function resizeImage($filepath, $maxWidth, $maxHeight) {
    try {
        $imageInfo = getimagesize($filepath);
        if (!$imageInfo) return false;
        
        $width = $imageInfo[0];
        $height = $imageInfo[1];
        $type = $imageInfo[2];
        
        // Check if resize is needed
        if ($width <= $maxWidth && $height <= $maxHeight) {
            return true;
        }
        
        // Calculate new dimensions
        $ratio = min($maxWidth / $width, $maxHeight / $height);
        $newWidth = round($width * $ratio);
        $newHeight = round($height * $ratio);
        
        // Create image resource based on type
        switch ($type) {
            case IMAGETYPE_JPEG:
                $source = imagecreatefromjpeg($filepath);
                break;
            case IMAGETYPE_PNG:
                $source = imagecreatefrompng($filepath);
                break;
            case IMAGETYPE_GIF:
                $source = imagecreatefromgif($filepath);
                break;
            case IMAGETYPE_WEBP:
                $source = imagecreatefromwebp($filepath);
                break;
            default:
                return false;
        }
        
        if (!$source) return false;
        
        // Create new image
        $destination = imagecreatetruecolor($newWidth, $newHeight);
        
        // Preserve transparency for PNG and GIF
        if ($type == IMAGETYPE_PNG || $type == IMAGETYPE_GIF) {
            imagealphablending($destination, false);
            imagesavealpha($destination, true);
            $transparent = imagecolorallocatealpha($destination, 255, 255, 255, 127);
            imagefilledrectangle($destination, 0, 0, $newWidth, $newHeight, $transparent);
        }
        
        // Resize image
        imagecopyresampled($destination, $source, 0, 0, 0, 0, $newWidth, $newHeight, $width, $height);
        
        // Save resized image
        switch ($type) {
            case IMAGETYPE_JPEG:
                imagejpeg($destination, $filepath, 85);
                break;
            case IMAGETYPE_PNG:
                imagepng($destination, $filepath, 8);
                break;
            case IMAGETYPE_GIF:
                imagegif($destination, $filepath);
                break;
            case IMAGETYPE_WEBP:
                imagewebp($destination, $filepath, 85);
                break;
        }
        
        // Clean up
        imagedestroy($source);
        imagedestroy($destination);
        
        return true;
        
    } catch (Exception $e) {
        error_log("Image resize error: " . $e->getMessage());
        return false;
    }
}

/**
 * Get service statistics
 */
function getServiceStats() {
    global $database;
    
    $stats = [];
    
    // Total services
    $stats['total'] = $database->fetch("SELECT COUNT(*) as count FROM services")['count'];
    
    // Active services
    $stats['active'] = $database->fetch("SELECT COUNT(*) as count FROM services WHERE is_active = 1")['count'];
    
    // Services by category
    $stats['by_category'] = $database->fetchAll(
        "SELECT category, COUNT(*) as count FROM services WHERE category IS NOT NULL GROUP BY category ORDER BY count DESC"
    );
    
    // Most booked services
    $stats['most_booked'] = $database->fetchAll(
        "SELECT s.name, COUNT(b.id) as booking_count 
         FROM services s 
         LEFT JOIN bookings b ON s.id = b.service_id 
         GROUP BY s.id, s.name 
         ORDER BY booking_count DESC 
         LIMIT 5"
    );
    
    // Average service price
    $stats['avg_price'] = $database->fetch("SELECT AVG(price) as avg_price FROM services WHERE is_active = 1")['avg_price'];
    
    return $stats;
}
?>
