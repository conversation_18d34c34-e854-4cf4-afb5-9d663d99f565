<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/customer_panel_functions.php';
require_once __DIR__ . '/../../includes/booking_functions.php';

// Check authentication and customer role
if (!isLoggedIn() || $_SESSION['user_role'] !== 'CUSTOMER') {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];

try {
    if ($method === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid JSON input']);
            exit;
        }
        
        $date = $input['date'] ?? '';
        $startTime = $input['start_time'] ?? '';
        $endTime = $input['end_time'] ?? '';
        $staffId = $input['staff_id'] ?? '';
        $serviceId = $input['service_id'] ?? '';
        $packageId = $input['package_id'] ?? '';

        // Validate required fields
        if (empty($date) || empty($startTime) || empty($endTime) || empty($staffId)) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'error' => 'Date, start time, end time, and staff ID are required'
            ]);
            exit;
        }

        if (empty($serviceId) && empty($packageId)) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'error' => 'Either service_id or package_id is required'
            ]);
            exit;
        }
        
        // Validate date format
        if (!DateTime::createFromFormat('Y-m-d', $date)) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'error' => 'Invalid date format'
            ]);
            exit;
        }
        
        // Validate time format
        if (!DateTime::createFromFormat('H:i', $startTime) || !DateTime::createFromFormat('H:i', $endTime)) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'error' => 'Invalid time format'
            ]);
            exit;
        }
        
        // Check if date is not in the past
        if ($date < date('Y-m-d')) {
            echo json_encode([
                'success' => false,
                'message' => 'Cannot book appointments in the past'
            ]);
            exit;
        }

        // Check if start time is before end time
        if ($startTime >= $endTime) {
            echo json_encode([
                'success' => false,
                'message' => 'Start time must be before end time'
            ]);
            exit;
        }

        // Validate staff exists and is active
        $staff = $database->fetch(
            "SELECT id, name FROM users WHERE id = ? AND role = 'STAFF' AND is_active = 1",
            [$staffId]
        );

        if (!$staff) {
            echo json_encode([
                'success' => false,
                'message' => 'Selected staff member is not available'
            ]);
            exit;
        }

        // Check if the requested time is within business hours (9 AM to 6 PM)
        $businessStart = '09:00';
        $businessEnd = '18:00';
        
        if ($startTime < $businessStart || $endTime > $businessEnd) {
            echo json_encode([
                'success' => false,
                'message' => 'Appointment time must be between 9:00 AM and 6:00 PM'
            ]);
            exit;
        }

        // Validate service/package duration matches the time slot
        if (!empty($serviceId)) {
            $service = $database->fetch("SELECT duration FROM services WHERE id = ? AND is_active = 1", [$serviceId]);
            if (!$service) {
                echo json_encode([
                    'success' => false,
                    'message' => 'Selected service is not available'
                ]);
                exit;
            }
            $expectedDuration = $service['duration'];
        } else {
            // Calculate package duration
            $packageServices = $database->fetchAll("
                SELECT s.duration
                FROM services s
                INNER JOIN package_services ps ON s.id = ps.service_id
                WHERE ps.package_id = ? AND s.is_active = 1
            ", [$packageId]);

            if (empty($packageServices)) {
                echo json_encode([
                    'success' => false,
                    'message' => 'Selected package is not available'
                ]);
                exit;
            }
            $expectedDuration = array_sum(array_column($packageServices, 'duration'));
        }

        // Calculate actual duration from start and end time
        $startDateTime = new DateTime($date . ' ' . $startTime);
        $endDateTime = new DateTime($date . ' ' . $endTime);
        $actualDuration = ($endDateTime->getTimestamp() - $startDateTime->getTimestamp()) / 60; // in minutes

        if (abs($actualDuration - $expectedDuration) > 5) { // Allow 5-minute tolerance
            echo json_encode([
                'success' => false,
                'message' => "Time slot duration ({$actualDuration} min) doesn't match service duration ({$expectedDuration} min)"
            ]);
            exit;
        }

        // Check staff availability using the existing function
        $isAvailable = checkStaffAvailability($staffId, $date, $startTime . ':00', $endTime . ':00');

        if ($isAvailable) {
            echo json_encode([
                'success' => true,
                'message' => $staff['name'] . ' is available at this time',
                'staff_name' => $staff['name'],
                'date' => $date,
                'start_time' => $startTime,
                'end_time' => $endTime
            ]);
        } else {
            // Get conflicting bookings for more specific message
            $conflictingBookings = $database->fetchAll(
                "SELECT start_time, end_time, u.name as customer_name
                 FROM bookings b
                 LEFT JOIN users u ON b.user_id = u.id
                 WHERE b.staff_id = ? AND b.date = ? AND b.status NOT IN ('CANCELLED', 'NO_SHOW') 
                 AND ((b.start_time < ? AND b.end_time > ?) OR (b.start_time < ? AND b.end_time > ?) 
                 OR (b.start_time >= ? AND b.end_time <= ?))
                 ORDER BY b.start_time",
                [$staffId, $date, $endTime . ':00', $startTime . ':00', $startTime . ':00', $endTime . ':00', $startTime . ':00', $endTime . ':00']
            );

            $conflictMessage = $staff['name'] . ' is already booked during this time.';
            if (!empty($conflictingBookings)) {
                $conflictTimes = [];
                foreach ($conflictingBookings as $booking) {
                    $conflictStart = date('g:i A', strtotime($booking['start_time']));
                    $conflictEnd = date('g:i A', strtotime($booking['end_time']));
                    $conflictTimes[] = "{$conflictStart} - {$conflictEnd}";
                }
                $conflictMessage .= ' Existing bookings: ' . implode(', ', $conflictTimes);
            }
            $conflictMessage .= ' Please choose a different time or date.';

            echo json_encode([
                'success' => false,
                'message' => $conflictMessage,
                'conflicts' => $conflictingBookings
            ]);
        }
        
    } else {
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Server error: ' . $e->getMessage()
    ]);
}
?>
