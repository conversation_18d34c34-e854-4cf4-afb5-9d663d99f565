<?php
require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/customer_panel_functions.php';

// Check if user is logged in as customer
if (!isLoggedIn() || $_SESSION['user_role'] !== 'CUSTOMER') {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

// Get search parameters
$searchTerm = $_GET['search'] ?? '';
$category = $_GET['category'] ?? '';

global $database;

try {
    $params = [];
    $serviceQuery = "SELECT * FROM services WHERE is_active = 1";
    $packageQuery = "SELECT p.*, GROUP_CONCAT(s.name) as included_services 
                    FROM packages p 
                    LEFT JOIN package_services ps ON p.id = ps.package_id 
                    LEFT JOIN services s ON ps.service_id = s.id 
                    WHERE p.is_active = 1";

    // Add search conditions if search term is provided
    if (!empty($searchTerm)) {
        $searchParam = "%$searchTerm%";
        $serviceQuery .= " AND (name LIKE ? OR description LIKE ?)";
        $packageQuery .= " AND (p.name LIKE ? OR p.description LIKE ? OR s.name LIKE ?)";
        $params = array_merge($params, [$searchParam, $searchParam]);
        $packageParams = [$searchParam, $searchParam, $searchParam];
    }

    // Add category filter if provided
    if (!empty($category)) {
        $serviceQuery .= " AND category = ?";
        $params[] = $category;
    }

    // Group packages by package ID
    $packageQuery .= " GROUP BY p.id";

    // Get services
    $services = $database->fetchAll($serviceQuery, $params);

    // Get packages with their services
    $packages = !empty($searchTerm) ? 
        $database->fetchAll($packageQuery, $packageParams) : 
        $database->fetchAll($packageQuery);

    // Calculate total duration and savings for packages
    foreach ($packages as &$package) {
        // Get all services in the package
        $packageServices = $database->fetchAll("
            SELECT s.* FROM services s
            INNER JOIN package_services ps ON s.id = ps.service_id
            WHERE ps.package_id = ?
        ", [$package['id']]);

        $package['services'] = $packageServices;
        $package['total_duration'] = array_sum(array_column($packageServices, 'duration'));
        
        // Calculate savings
        $originalPrice = array_sum(array_column($packageServices, 'price'));
        $package['savings'] = max(0, $originalPrice - $package['price']);
    }

    echo json_encode([
        'success' => true,
        'services' => $services,
        'packages' => $packages
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'An error occurred while searching'
    ]);
} 