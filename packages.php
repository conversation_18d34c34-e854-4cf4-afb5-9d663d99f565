<?php
/**
 * Packages Page
 * Flix Salon & SPA - PHP Version
 */

require_once __DIR__ . '/config/app.php';

// Get all active packages with calculated fields
$packages = $database->fetchAll("
    SELECT
        p.*,
        COALESCE(SUM(s.price), 0) as original_price,
        COALESCE(SUM(s.duration), 0) as duration,
        COUNT(ps.service_id) as service_count
    FROM packages p
    LEFT JOIN package_services ps ON p.id = ps.package_id
    LEFT JOIN services s ON ps.service_id = s.id AND s.is_active = 1
    WHERE p.is_active = 1
    GROUP BY p.id
    ORDER BY p.price ASC
");

// Process packages to add additional calculated fields
foreach ($packages as $index => $package) {
    // Calculate discount percentage
    if ($package['original_price'] > 0 && $package['original_price'] > $package['price']) {
        $packages[$index]['discount_percentage'] = round((($package['original_price'] - $package['price']) / $package['original_price']) * 100);
    } else {
        $packages[$index]['discount_percentage'] = 0;
    }

    // Set default validity days (you can make this configurable later)
    $packages[$index]['validity_days'] = 30;

    // Get services list for this package
    $packageServices = $database->fetchAll("
        SELECT s.name
        FROM package_services ps
        JOIN services s ON ps.service_id = s.id
        WHERE ps.package_id = ? AND s.is_active = 1
        ORDER BY s.name
    ", [$package['id']]);

    $packages[$index]['services'] = array_column($packageServices, 'name');
}

$pageTitle = "Service Packages";
include __DIR__ . '/includes/header.php';
?>

    <!-- Hero Section -->
    <section class="relative py-24 bg-gradient-to-br from-salon-black via-salon-black to-salon-black">
        <div class="absolute inset-0 bg-gradient-to-r from-salon-gold/5 to-transparent"></div>
        <div class="relative max-w-7xl mx-auto px-6 text-center">
            <div class="inline-block bg-salon-gold/10 text-salon-gold px-4 py-2 rounded-full text-sm font-semibold mb-6">
                Special Packages
            </div>
            <h1 class="text-5xl md:text-7xl font-bold font-serif text-white mb-6">
                Beauty <span class="text-salon-gold">Packages</span>
            </h1>
            <p class="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
                Save more with our carefully curated service packages designed to give you the complete beauty experience at exceptional value.
            </p>
        </div>
    </section>

    <!-- Packages Grid -->
    <section class="py-24 bg-salon-black">
        <div class="max-w-7xl mx-auto px-6">
            <?php if (empty($packages)): ?>
                <!-- No Packages Available -->
                <div class="text-center py-16">
                    <i class="fas fa-gift text-6xl text-gray-600 mb-6"></i>
                    <h3 class="text-2xl font-bold text-white mb-4">Packages Coming Soon</h3>
                    <p class="text-gray-400 mb-8">We're working on creating amazing package deals for you. Check back soon!</p>
                    <a href="<?= getBasePath() ?>/services.php" class="bg-salon-gold hover:bg-yellow-500 text-black px-8 py-3 rounded-lg font-semibold transition-colors">
                        View Individual Services
                    </a>
                </div>
            <?php else: ?>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <?php foreach ($packages as $index => $package): ?>
                        <div class="group relative">
                            <?php if ($index === 1): ?>
                                <!-- Popular Badge for middle package -->
                                <div class="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
                                    <div class="bg-salon-gold text-black px-4 py-2 rounded-full text-sm font-bold">
                                        Most Popular
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 rounded-2xl overflow-hidden hover:border-salon-gold/50 transition-all duration-300 hover:scale-105 hover:shadow-xl <?= $index === 1 ? 'ring-2 ring-salon-gold/30' : '' ?>">
                                <!-- Package Header with Lazy Loading -->
                                <div class="relative h-48 bg-gradient-to-br from-salon-gold/20 to-secondary-900 overflow-hidden">
                                    <?php if ($package['image']): ?>
                                        <!-- Lazy Loading Placeholder -->
                                        <div class="lazy-placeholder w-full h-full bg-gradient-to-br from-salon-gold/10 to-secondary-900 flex items-center justify-center">
                                            <div class="text-center">
                                                <div class="animate-pulse">
                                                    <div class="w-12 h-12 bg-salon-gold/20 rounded-full mx-auto mb-2"></div>
                                                    <div class="h-2 bg-salon-gold/20 rounded w-20 mx-auto"></div>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- Actual Image (lazy loaded) -->
                                        <img data-src="<?= htmlspecialchars($package['image']) ?>"
                                             alt="<?= htmlspecialchars($package['name']) ?>"
                                             class="lazy-image w-full h-full object-cover group-hover:scale-110 transition-transform duration-500 opacity-0"
                                             loading="lazy">
                                    <?php else: ?>
                                        <div class="w-full h-full flex items-center justify-center">
                                            <div class="text-center">
                                                <i class="fas fa-spa text-salon-gold/60 text-4xl mb-2"></i>
                                                <div class="text-gray-300 text-sm">Beauty Package</div>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                    <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>

                                    <!-- Savings Badge -->
                                    <?php if (isset($package['discount_percentage']) && $package['discount_percentage'] > 0): ?>
                                        <div class="absolute top-4 right-4">
                                            <div class="bg-red-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                                                Save <?= $package['discount_percentage'] ?>%
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <!-- Package Content -->
                                <div class="p-6">
                                    <h3 class="text-2xl font-bold text-white mb-2 group-hover:text-salon-gold transition-colors duration-300">
                                        <?= htmlspecialchars($package['name']) ?>
                                    </h3>
                                    
                                    <p class="text-gray-300 mb-4 leading-relaxed">
                                        <?= htmlspecialchars($package['description']) ?>
                                    </p>
                                    
                                    <!-- Services Included -->
                                    <?php
                                    $services = $package['services'] ?? [];
                                    if (!empty($services)): ?>
                                        <div class="mb-4">
                                            <h4 class="text-sm font-semibold text-salon-gold mb-2">Includes:</h4>
                                            <ul class="space-y-1">
                                                <?php foreach ($services as $service): ?>
                                                    <li class="flex items-center text-sm text-gray-300">
                                                        <i class="fas fa-check text-salon-gold mr-2 text-xs"></i>
                                                        <?= htmlspecialchars($service) ?>
                                                    </li>
                                                <?php endforeach; ?>
                                            </ul>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <!-- Package Details -->
                                    <div class="flex items-center justify-between mb-4 text-sm text-gray-400">
                                        <div class="flex items-center">
                                            <i class="fas fa-clock mr-1"></i>
                                            <?= $package['duration'] ?> minutes
                                        </div>
                                        <div class="flex items-center">
                                            <i class="fas fa-calendar mr-1"></i>
                                            Valid <?= $package['validity_days'] ?> days
                                        </div>
                                    </div>
                                    
                                    <!-- Pricing -->
                                    <div class="mb-6">
                                        <?php if ($package['original_price'] > $package['price']): ?>
                                            <div class="flex items-center gap-2 mb-1">
                                                <span class="text-lg text-gray-400 line-through"><?= formatCurrency($package['original_price']) ?></span>
                                                <span class="text-sm bg-red-100 text-red-600 px-2 py-1 rounded-full">
                                                    Save <?= formatCurrency($package['original_price'] - $package['price']) ?>
                                                </span>
                                            </div>
                                        <?php endif; ?>
                                        <div class="text-3xl font-bold text-salon-gold">
                                            <?= formatCurrency($package['price']) ?>
                                        </div>
                                    </div>
                                    
                                    <!-- Features -->
                                    <div class="space-y-2 mb-6">
                                        <div class="flex items-center text-sm text-gray-300">
                                            <i class="fas fa-star text-salon-gold mr-2"></i>
                                            Premium products included
                                        </div>
                                        <div class="flex items-center text-sm text-gray-300">
                                            <i class="fas fa-user-tie text-salon-gold mr-2"></i>
                                            Expert stylists
                                        </div>
                                        <div class="flex items-center text-sm text-gray-300">
                                            <i class="fas fa-coffee text-salon-gold mr-2"></i>
                                            Complimentary refreshments
                                        </div>
                                    </div>
                                    
                                    <div class="flex gap-3">
                                        <button onclick="bookPackage('<?= $package['id'] ?>', '<?= htmlspecialchars($package['name']) ?>', <?= $package['price'] ?>, <?= $package['duration'] ?>)"
                                                class="flex-1 bg-salon-gold hover:bg-yellow-500 text-black py-3 px-4 rounded-lg font-semibold transition-all duration-300 hover:scale-105">
                                            Book This Package
                                        </button>

                                        <!-- Wishlist Heart Icon -->
                                        <button onclick="toggleWishlist('package', '<?= $package['id'] ?>', this)"
                                                class="wishlist-btn px-4 py-3 border-2 border-gray-500 text-gray-300 rounded-lg hover:border-red-400 hover:text-red-400 transition-all duration-300"
                                                data-item-type="package"
                                                data-item-id="<?= $package['id'] ?>"
                                                title="Add to wishlist">
                                            <i class="far fa-heart text-lg"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </section>

    <!-- Package Benefits -->
    <section class="py-20 bg-secondary-900">
        <div class="max-w-7xl mx-auto px-6">
            <div class="text-center mb-16">
                <div class="inline-block bg-salon-gold/10 text-salon-gold px-4 py-2 rounded-full text-sm font-semibold mb-6">
                    Package Benefits
                </div>
                <h2 class="text-4xl md:text-5xl font-bold font-serif text-white mb-6">
                    Why Choose Our <span class="text-salon-gold">Packages</span>?
                </h2>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="text-center group">
                    <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-salon-gold/30 transition-colors">
                        <i class="fas fa-percentage text-salon-gold text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-bold text-white mb-2">Better Value</h3>
                    <p class="text-gray-300 text-sm">Save up to 30% compared to booking services individually</p>
                </div>

                <div class="text-center group">
                    <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-salon-gold/30 transition-colors">
                        <i class="fas fa-clock text-salon-gold text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-bold text-white mb-2">Time Efficient</h3>
                    <p class="text-gray-300 text-sm">Complete transformation in one convenient session</p>
                </div>

                <div class="text-center group">
                    <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-salon-gold/30 transition-colors">
                        <i class="fas fa-palette text-salon-gold text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-bold text-white mb-2">Coordinated Look</h3>
                    <p class="text-gray-300 text-sm">Services designed to complement each other perfectly</p>
                </div>

                <div class="text-center group">
                    <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-salon-gold/30 transition-colors">
                        <i class="fas fa-gift text-salon-gold text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-bold text-white mb-2">Bonus Perks</h3>
                    <p class="text-gray-300 text-sm">Exclusive add-ons and complimentary services included</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action -->
    <section class="py-20 bg-salon-black">
        <div class="max-w-4xl mx-auto px-6 text-center">
            <div class="bg-gradient-to-r from-salon-gold/10 to-transparent border border-salon-gold/20 rounded-3xl p-12">
                <h2 class="text-3xl md:text-4xl font-bold font-serif text-white mb-6">
                    Ready for Your <span class="text-salon-gold">Complete Makeover</span>?
                </h2>
                <p class="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
                    Choose from our carefully curated packages or contact us to create a custom package tailored to your specific needs.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="<?= getBasePath() ?>/customer/book" class="bg-salon-gold hover:bg-yellow-500 text-black px-8 py-4 rounded-xl font-semibold transition-all duration-300 hover:scale-105">
                        <i class="fas fa-calendar-plus mr-2"></i>
                        Book Package
                    </a>
                    <a href="<?= getBasePath() ?>/contact.php" class="bg-secondary-900 hover:bg-secondary-800 text-white px-8 py-4 rounded-xl font-semibold transition-all duration-300 hover:scale-105 border border-secondary-700">
                        <i class="fas fa-phone mr-2"></i>
                        Custom Package
                    </a>
                </div>
            </div>
        </div>
    </section>

<script>
// Initialize wishlist functionality and lazy loading when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeWishlistStates();
    new PackageLazyLoader();
});

// CSRF Token
const csrfToken = '<?= $_SESSION['csrf_token'] ?? '' ?>';

// Initialize wishlist heart states
function initializeWishlistStates() {
    const wishlistBtns = document.querySelectorAll('.wishlist-btn');

    wishlistBtns.forEach(btn => {
        const itemType = btn.dataset.itemType;
        const itemId = btn.dataset.itemId;

        // Check if item is in wishlist
        fetch(`<?= getBasePath() ?>/api/wishlist.php?action=check&item_type=${itemType}&item_id=${itemId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.inWishlist) {
                    updateHeartIcon(btn, true);
                }
            })
            .catch(error => console.error('Error checking wishlist:', error));
    });
}

// Toggle wishlist item
function toggleWishlist(itemType, itemId, button) {
    // Prevent event bubbling
    event.stopPropagation();

    // Add loading state
    const icon = button.querySelector('i');
    const originalClass = icon.className;
    icon.className = 'fas fa-spinner fa-spin text-white';

    fetch('<?= getBasePath() ?>/api/wishlist.php?action=toggle', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `item_type=${itemType}&item_id=${itemId}&csrf_token=${csrfToken}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateHeartIcon(button, data.inWishlist);
            showToast(data.message, 'success');
        } else {
            showToast(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('An error occurred. Please try again.', 'error');
    })
    .finally(() => {
        // Restore original icon if there was an error
        if (icon.className.includes('fa-spinner')) {
            icon.className = originalClass;
        }
    });
}

// Update heart icon appearance
function updateHeartIcon(button, inWishlist) {
    const icon = button.querySelector('i');

    if (inWishlist) {
        icon.className = 'fas fa-heart text-lg';
        button.classList.remove('border-gray-500', 'text-gray-300', 'hover:border-red-400', 'hover:text-red-400');
        button.classList.add('border-red-400', 'text-red-400', 'bg-red-50', 'animate-pulse');
        setTimeout(() => button.classList.remove('animate-pulse'), 600);
    } else {
        icon.className = 'far fa-heart text-lg';
        button.classList.remove('border-red-400', 'text-red-400', 'bg-red-50');
        button.classList.add('border-gray-500', 'text-gray-300', 'hover:border-red-400', 'hover:text-red-400');
    }
}

// Show toast notification with smooth animations
function showToast(message, type = 'success') {
    // Remove any existing toast first
    const existingToast = document.getElementById('wishlist-toast');
    if (existingToast) {
        existingToast.style.transform = 'translateX(100%)';
        existingToast.style.opacity = '0';
        setTimeout(() => existingToast.remove(), 200);
    }

    // Create new toast
    const toast = document.createElement('div');
    toast.id = 'wishlist-toast';

    // Set base styles for smooth animation
    toast.style.cssText = `
        position: fixed;
        top: 1rem;
        right: 1rem;
        z-index: 9999;
        background-color: #1e293b;
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 0.75rem;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        transform: translateX(100%) scale(0.95);
        opacity: 0;
        transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
        max-width: 20rem;
        backdrop-filter: blur(8px);
    `;

    // Set content and style based on type
    if (type === 'success') {
        toast.style.borderLeft = '4px solid #10b981';
        toast.innerHTML = `
            <div style="display: flex; align-items: center;">
                <i class="fas fa-check-circle" style="color: #10b981; margin-right: 0.75rem; font-size: 1.125rem;"></i>
                <span style="font-size: 0.875rem; font-weight: 500; line-height: 1.25;">${message}</span>
            </div>
        `;
    } else {
        toast.style.borderLeft = '4px solid #ef4444';
        toast.innerHTML = `
            <div style="display: flex; align-items: center;">
                <i class="fas fa-exclamation-circle" style="color: #ef4444; margin-right: 0.75rem; font-size: 1.125rem;"></i>
                <span style="font-size: 0.875rem; font-weight: 500; line-height: 1.25;">${message}</span>
            </div>
        `;
    }

    // Add to DOM
    document.body.appendChild(toast);

    // Trigger smooth slide-in animation
    requestAnimationFrame(() => {
        requestAnimationFrame(() => {
            toast.style.transform = 'translateX(0) scale(1)';
            toast.style.opacity = '1';
        });
    });

    // Hide toast after 2 seconds with smooth slide-out
    setTimeout(() => {
        toast.style.transform = 'translateX(100%) scale(0.95)';
        toast.style.opacity = '0';

        // Remove from DOM after animation completes
        setTimeout(() => {
            if (toast && toast.parentNode) {
                toast.remove();
            }
        }, 400);
    }, 2000);
}

// Book package function - similar to bookService in services.php
function bookPackage(packageId, packageName, price, duration) {
    // Check if user is logged in
    <?php if (isLoggedIn()): ?>
        // Redirect to booking page with package pre-selected
        window.location.href = `<?= getBasePath() ?>/customer/book/?package=${packageId}`;
    <?php else: ?>
        // Store package selection in sessionStorage for persistence across authentication
        const packageSelection = {
            packageId: packageId,
            packageName: packageName,
            price: price,
            duration: duration,
            timestamp: Date.now()
        };
        sessionStorage.setItem('pendingPackageBooking', JSON.stringify(packageSelection));

        // Redirect to login page with return URL
        if (confirm('Please log in to book an appointment. Would you like to log in now?')) {
            window.location.href = `<?= getBasePath() ?>/auth/login.php?redirect=${encodeURIComponent('/customer/book/?package=' + packageId)}`;
        }
    <?php endif; ?>
}

// Lazy Loading Implementation for Packages
class PackageLazyLoader {
    constructor() {
        this.imageObserver = null;
        this.init();
    }

    init() {
        // Check if Intersection Observer is supported
        if ('IntersectionObserver' in window) {
            this.imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.loadImage(entry.target);
                        observer.unobserve(entry.target);
                    }
                });
            }, {
                rootMargin: '50px 0px',
                threshold: 0.01
            });

            this.observeImages();
        } else {
            // Fallback for browsers without Intersection Observer
            this.loadAllImages();
        }

        // Add lazy loading styles
        this.addStyles();
    }

    observeImages() {
        const lazyImages = document.querySelectorAll('.lazy-image');
        lazyImages.forEach(img => {
            this.imageObserver.observe(img);
        });
    }

    loadImage(img) {
        const placeholder = img.previousElementSibling;

        // Create a new image to preload
        const imageLoader = new Image();

        imageLoader.onload = () => {
            // Image loaded successfully
            img.src = img.dataset.src;
            img.classList.remove('opacity-0');
            img.classList.add('opacity-100');

            // Hide placeholder with fade effect
            if (placeholder && placeholder.classList.contains('lazy-placeholder')) {
                placeholder.style.transition = 'opacity 0.3s ease-out';
                placeholder.style.opacity = '0';
                setTimeout(() => {
                    placeholder.style.display = 'none';
                }, 300);
            }

            // Add loaded class for any additional styling
            img.classList.add('lazy-loaded');
        };

        imageLoader.onerror = () => {
            // Handle image load error
            img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMzMzIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vdCBmb3VuZDwvdGV4dD48L3N2Zz4=';
            img.classList.remove('opacity-0');
            img.classList.add('opacity-100');

            if (placeholder && placeholder.classList.contains('lazy-placeholder')) {
                placeholder.style.display = 'none';
            }
        };

        // Start loading the image
        imageLoader.src = img.dataset.src;
    }

    loadAllImages() {
        // Fallback: load all images immediately
        const lazyImages = document.querySelectorAll('.lazy-image');
        lazyImages.forEach(img => {
            this.loadImage(img);
        });
    }

    addStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .lazy-image {
                transition: opacity 0.3s ease-in-out;
            }

            .lazy-placeholder {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 1;
            }

            .lazy-loaded {
                z-index: 2;
            }

            /* Pulse animation for loading placeholder */
            @keyframes pulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.5; }
            }

            .animate-pulse {
                animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
            }
        `;
        document.head.appendChild(style);
    }
}
</script>

<?php include __DIR__ . '/includes/footer.php'; ?>
