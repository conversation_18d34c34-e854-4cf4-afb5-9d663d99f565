<?php
/**
 * Offer Management Functions
 * Handles all offer-related operations including CRUD, validation, and statistics
 */

/**
 * Get all offers with optional filtering
 */
function getAllOffers($search = '', $status = 'all', $limit = null, $offset = 0) {
    global $database;
    
    $conditions = [];
    $params = [];
    
    if (!empty($search)) {
        $conditions[] = "(title LIKE ? OR code LIKE ? OR description LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }
    
    if ($status !== 'all') {
        $conditions[] = "is_active = ?";
        $params[] = $status === 'active' ? 1 : 0;
    }
    
    $whereClause = !empty($conditions) ? 'WHERE ' . implode(' AND ', $conditions) : '';
    $limitClause = $limit ? "LIMIT $limit OFFSET $offset" : '';
    
    $sql = "SELECT 
                o.*,
                COUNT(b.id) as usage_count
            FROM offers o
            LEFT JOIN bookings b ON o.id = b.offer_id
            $whereClause
            GROUP BY o.id
            ORDER BY o.created_at DESC
            $limitClause";
    
    return $database->fetchAll($sql, $params);
}

/**
 * Get offer by ID
 */
function getOfferById($id) {
    global $database;
    
    $sql = "SELECT 
                o.*,
                COUNT(b.id) as usage_count
            FROM offers o
            LEFT JOIN bookings b ON o.id = b.offer_id
            WHERE o.id = ?
            GROUP BY o.id";
    
    return $database->fetch($sql, [$id]);
}

/**
 * Get offer by code
 */
function getOfferByCode($code) {
    global $database;
    
    $sql = "SELECT 
                o.*,
                COUNT(b.id) as usage_count
            FROM offers o
            LEFT JOIN bookings b ON o.id = b.offer_id
            WHERE o.code = ? AND o.is_active = 1
            GROUP BY o.id";
    
    return $database->fetch($sql, [$code]);
}

/**
 * Create new offer
 */
function createOffer($data) {
    global $database;
    
    // Validate required fields
    $required = ['title', 'description', 'discount', 'code', 'valid_from', 'valid_to'];
    foreach ($required as $field) {
        if (empty($data[$field])) {
            throw new Exception("Field '$field' is required");
        }
    }
    
    // Validate discount
    if ($data['discount'] < 0 || $data['discount'] > 100) {
        throw new Exception("Discount must be between 0 and 100");
    }
    
    // Validate dates
    $validFrom = new DateTime($data['valid_from']);
    $validTo = new DateTime($data['valid_to']);
    if ($validFrom >= $validTo) {
        throw new Exception("Valid from date must be before valid to date");
    }
    
    // Check if code already exists
    if (isOfferCodeExists($data['code'])) {
        throw new Exception("Offer code already exists");
    }
    
    $id = generateUUID();
    
    $sql = "INSERT INTO offers (
                id, title, description, discount, code, valid_from, valid_to,
                max_usage, is_active, image, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";
    
    $params = [
        $id,
        $data['title'],
        $data['description'],
        $data['discount'],
        $data['code'],
        $data['valid_from'],
        $data['valid_to'],
        $data['max_usage'] ?? null,
        $data['is_active'] ?? 1,
        $data['image'] ?? null
    ];
    
    $database->execute($sql, $params);
    
    return getOfferById($id);
}

/**
 * Update offer
 */
function updateOffer($id, $data) {
    global $database;
    
    $offer = getOfferById($id);
    if (!$offer) {
        throw new Exception("Offer not found");
    }
    
    // Validate discount if provided
    if (isset($data['discount']) && ($data['discount'] < 0 || $data['discount'] > 100)) {
        throw new Exception("Discount must be between 0 and 100");
    }
    
    // Validate dates if provided
    if (isset($data['valid_from']) && isset($data['valid_to'])) {
        $validFrom = new DateTime($data['valid_from']);
        $validTo = new DateTime($data['valid_to']);
        if ($validFrom >= $validTo) {
            throw new Exception("Valid from date must be before valid to date");
        }
    }
    
    // Check if code already exists (excluding current offer)
    if (isset($data['code']) && $data['code'] !== $offer['code']) {
        if (isOfferCodeExists($data['code'], $id)) {
            throw new Exception("Offer code already exists");
        }
    }
    
    $updateFields = [];
    $params = [];
    
    $allowedFields = ['title', 'description', 'discount', 'code', 'valid_from', 'valid_to', 'max_usage', 'is_active', 'image'];
    
    foreach ($allowedFields as $field) {
        if (isset($data[$field])) {
            $updateFields[] = "$field = ?";
            $params[] = $data[$field];
        }
    }
    
    if (empty($updateFields)) {
        throw new Exception("No fields to update");
    }
    
    $updateFields[] = "updated_at = NOW()";
    $params[] = $id;
    
    $sql = "UPDATE offers SET " . implode(', ', $updateFields) . " WHERE id = ?";
    
    $database->execute($sql, $params);
    
    return getOfferById($id);
}

/**
 * Delete offer
 */
function deleteOffer($id) {
    global $database;
    
    $offer = getOfferById($id);
    if (!$offer) {
        throw new Exception("Offer not found");
    }
    
    // Check if offer is being used in bookings
    $usageCount = $database->fetch("SELECT COUNT(*) as count FROM bookings WHERE offer_id = ?", [$id]);
    if ($usageCount['count'] > 0) {
        throw new Exception("Cannot delete offer that has been used in bookings");
    }
    
    $database->execute("DELETE FROM offers WHERE id = ?", [$id]);
    
    return true;
}

/**
 * Check if offer code exists
 */
function isOfferCodeExists($code, $excludeId = null) {
    global $database;
    
    $sql = "SELECT COUNT(*) as count FROM offers WHERE code = ?";
    $params = [$code];
    
    if ($excludeId) {
        $sql .= " AND id != ?";
        $params[] = $excludeId;
    }
    
    $result = $database->fetch($sql, $params);
    return $result['count'] > 0;
}

/**
 * Validate offer for booking
 */
function validateOfferForBooking($code, $userId = null) {
    global $database;
    
    $offer = getOfferByCode($code);
    if (!$offer) {
        return ['valid' => false, 'message' => 'Invalid offer code'];
    }
    
    // Check if offer is active
    if (!$offer['is_active']) {
        return ['valid' => false, 'message' => 'Offer is not active'];
    }
    
    // Check date validity
    $now = new DateTime();
    $validFrom = new DateTime($offer['valid_from']);
    $validTo = new DateTime($offer['valid_to']);
    
    if ($now < $validFrom) {
        return ['valid' => false, 'message' => 'Offer is not yet valid'];
    }
    
    if ($now > $validTo) {
        return ['valid' => false, 'message' => 'Offer has expired'];
    }
    
    // Check usage limit
    if ($offer['max_usage'] && $offer['usage_count'] >= $offer['max_usage']) {
        return ['valid' => false, 'message' => 'Offer usage limit reached'];
    }
    
    return [
        'valid' => true,
        'offer' => $offer,
        'discount' => $offer['discount']
    ];
}

/**
 * Get offer statistics
 */
function getOfferStatistics() {
    global $database;
    
    $stats = [];
    
    // Total offers
    $stats['total_offers'] = $database->fetch("SELECT COUNT(*) as count FROM offers")['count'];
    
    // Active offers
    $stats['active_offers'] = $database->fetch("SELECT COUNT(*) as count FROM offers WHERE is_active = 1")['count'];
    
    // Expired offers
    $stats['expired_offers'] = $database->fetch("SELECT COUNT(*) as count FROM offers WHERE valid_to < NOW()")['count'];
    
    // Total usage
    $stats['total_usage'] = $database->fetch("SELECT COUNT(*) as count FROM bookings WHERE offer_id IS NOT NULL")['count'];
    
    // Total discount given
    $discountResult = $database->fetch("
        SELECT COALESCE(SUM(b.total_amount * (o.discount / 100)), 0) as total_discount
        FROM bookings b
        JOIN offers o ON b.offer_id = o.id
        WHERE b.status = 'COMPLETED'
    ");
    $stats['total_discount_given'] = $discountResult['total_discount'];
    
    return $stats;
}

/**
 * Get top performing offers
 */
function getTopOffers($limit = 5) {
    global $database;
    
    $sql = "SELECT 
                o.*,
                COUNT(b.id) as usage_count,
                COALESCE(SUM(CASE WHEN b.status = 'COMPLETED' THEN b.total_amount ELSE 0 END), 0) as revenue_generated
            FROM offers o
            LEFT JOIN bookings b ON o.id = b.offer_id
            GROUP BY o.id
            ORDER BY usage_count DESC, revenue_generated DESC
            LIMIT ?";
    
    return $database->fetchAll($sql, [$limit]);
}

/**
 * Get offers expiring soon
 */
function getExpiringOffers($days = 7) {
    global $database;
    
    $sql = "SELECT * FROM offers 
            WHERE is_active = 1 
            AND valid_to BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL ? DAY)
            ORDER BY valid_to ASC";
    
    return $database->fetchAll($sql, [$days]);
}
