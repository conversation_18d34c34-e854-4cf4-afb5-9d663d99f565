<?php
/**
 * Package Management Functions
 * Flix Salonce - PHP Version
 */

/**
 * Create a new package
 */
function createPackage($data) {
    global $database;

    try {
        // Include upload functions
        require_once __DIR__ . '/upload_functions.php';

        // Validate required fields
        if (empty($data['name'])) {
            return ['success' => false, 'error' => 'Package name is required'];
        }

        if (empty($data['price'])) {
            return ['success' => false, 'error' => 'Package price is required'];
        }

        if (empty($data['services'])) {
            return ['success' => false, 'error' => 'Services are required'];
        }

        if (!is_array($data['services']) || count($data['services']) < 2) {
            return ['success' => false, 'error' => 'Package must include at least 2 services'];
        }

        // Handle image upload or URL
        $imageValue = '';
        if (isset($_FILES['image_file']) && $_FILES['image_file']['error'] === UPLOAD_ERR_OK) {
            // File upload
            $uploadResult = uploadPackageImage($_FILES['image_file']);
            if (!$uploadResult['success']) {
                return ['success' => false, 'error' => $uploadResult['error']];
            }
            $imageValue = $uploadResult['filename'];
        } elseif (!empty($data['image_url'])) {
            // URL provided
            $imageValue = sanitize($data['image_url']);
        }
        
        $packageId = generateUUID();

        // Start transaction
        $database->beginTransaction();

        // Prepare description - provide default if empty
        $description = !empty($data['description']) ? sanitize($data['description']) : 'Package description';

        // Create package
        $database->query(
            "INSERT INTO packages (id, name, description, price, image, is_active, created_at, updated_at)
             VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())",
            [
                $packageId,
                sanitize($data['name']),
                $description,
                floatval($data['price']),
                $imageValue,
                isset($data['is_active']) ? 1 : 0
            ]
        );

        // Add services to package
        foreach ($data['services'] as $serviceId) {
            $packageServiceId = generateUUID();

            // Try with created_at first, fallback without it if column doesn't exist
            try {
                $database->query(
                    "INSERT INTO package_services (id, package_id, service_id, created_at)
                     VALUES (?, ?, ?, NOW())",
                    [$packageServiceId, $packageId, $serviceId]
                );
            } catch (Exception $e) {
                // If created_at column doesn't exist, try without it
                $database->query(
                    "INSERT INTO package_services (id, package_id, service_id)
                     VALUES (?, ?, ?)",
                    [$packageServiceId, $packageId, $serviceId]
                );
            }
        }
        
        $database->commit();
        
        return ['success' => true, 'id' => $packageId];
        
    } catch (Exception $e) {
        $database->rollback();
        error_log("Package creation error: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to create package'];
    }
}

/**
 * Update an existing package
 */
function updatePackage($packageId, $data) {
    global $database;

    try {
        // Include upload functions
        require_once __DIR__ . '/upload_functions.php';

        // Validate required fields
        if (empty($data['name']) || empty($data['price']) || empty($data['services'])) {
            return ['success' => false, 'error' => 'Name, price, and services are required'];
        }

        if (!is_array($data['services']) || count($data['services']) < 2) {
            return ['success' => false, 'error' => 'Package must include at least 2 services'];
        }

        // Get current package data
        $currentPackage = getPackageById($packageId);
        if (!$currentPackage) {
            return ['success' => false, 'error' => 'Package not found'];
        }

        // Handle image upload or URL
        $imageValue = $currentPackage['image']; // Keep current image by default

        if (isset($_FILES['image_file']) && $_FILES['image_file']['error'] === UPLOAD_ERR_OK) {
            // New file upload
            $uploadResult = uploadPackageImage($_FILES['image_file']);
            if (!$uploadResult['success']) {
                return ['success' => false, 'error' => $uploadResult['error']];
            }

            // Delete old image if it was an uploaded file
            if (!empty($currentPackage['image']) && !filter_var($currentPackage['image'], FILTER_VALIDATE_URL)) {
                deletePackageImage($currentPackage['image']);
            }

            $imageValue = $uploadResult['filename'];
        } elseif (!empty($data['image_url'])) {
            // URL provided
            // Delete old image if it was an uploaded file
            if (!empty($currentPackage['image']) && !filter_var($currentPackage['image'], FILTER_VALIDATE_URL)) {
                deletePackageImage($currentPackage['image']);
            }

            $imageValue = sanitize($data['image_url']);
        }
        
        // Check if package exists
        $currentPackage = $database->fetch("SELECT * FROM packages WHERE id = ?", [$packageId]);
        if (!$currentPackage) {
            return ['success' => false, 'error' => 'Package not found'];
        }
        
        // Start transaction
        $database->beginTransaction();
        
        // Update package
        $database->query(
            "UPDATE packages SET name = ?, description = ?, price = ?, image = ?, is_active = ?, updated_at = NOW()
             WHERE id = ?",
            [
                sanitize($data['name']),
                sanitize($data['description'] ?? ''),
                floatval($data['price']),
                $imageValue,
                isset($data['is_active']) ? 1 : 0,
                $packageId
            ]
        );
        
        // Remove existing services
        $database->query("DELETE FROM package_services WHERE package_id = ?", [$packageId]);
        
        // Add new services
        foreach ($data['services'] as $serviceId) {
            $packageServiceId = generateUUID();
            $database->query(
                "INSERT INTO package_services (id, package_id, service_id, created_at) 
                 VALUES (?, ?, ?, NOW())",
                [$packageServiceId, $packageId, $serviceId]
            );
        }
        
        $database->commit();
        
        return ['success' => true];
        
    } catch (Exception $e) {
        $database->rollback();
        error_log("Package update error: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to update package'];
    }
}

/**
 * Delete a package
 */
function deletePackage($packageId) {
    global $database;
    
    try {
        // Check if package has bookings
        $bookingCount = $database->fetch(
            "SELECT COUNT(*) as count FROM bookings WHERE package_id = ?",
            [$packageId]
        )['count'];
        
        if ($bookingCount > 0) {
            return ['success' => false, 'error' => 'Cannot delete package with existing bookings'];
        }
        
        // Start transaction
        $database->beginTransaction();
        
        // Delete package services
        $database->query("DELETE FROM package_services WHERE package_id = ?", [$packageId]);
        
        // Delete package
        $database->query("DELETE FROM packages WHERE id = ?", [$packageId]);
        
        $database->commit();
        
        return ['success' => true];
        
    } catch (Exception $e) {
        $database->rollback();
        error_log("Package deletion error: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to delete package'];
    }
}

/**
 * Get package by ID with services
 */
function getPackageById($packageId) {
    global $database;
    
    try {
        $package = $database->fetch("SELECT * FROM packages WHERE id = ?", [$packageId]);
        
        if ($package) {
            $package['services'] = getPackageServices($packageId);
        }
        
        return $package;
        
    } catch (Exception $e) {
        error_log("Get package error: " . $e->getMessage());
        return null;
    }
}

/**
 * Get services included in a package
 */
function getPackageServices($packageId) {
    global $database;
    
    return $database->fetchAll(
        "SELECT s.* FROM services s
         INNER JOIN package_services ps ON s.id = ps.service_id
         WHERE ps.package_id = ?
         ORDER BY s.name",
        [$packageId]
    );
}

/**
 * Get all active packages
 */
function getActivePackages() {
    global $database;
    
    return $database->fetchAll(
        "SELECT * FROM packages WHERE is_active = 1 ORDER BY name"
    );
}

/**
 * Get package statistics
 */
function getPackageStats() {
    global $database;
    
    $stats = [];
    
    // Total packages
    $stats['total'] = $database->fetch("SELECT COUNT(*) as count FROM packages")['count'];
    
    // Active packages
    $stats['active'] = $database->fetch("SELECT COUNT(*) as count FROM packages WHERE is_active = 1")['count'];
    
    // Total revenue from packages
    $stats['total_revenue'] = $database->fetch(
        "SELECT COALESCE(SUM(total_amount), 0) as revenue 
         FROM bookings 
         WHERE package_id IS NOT NULL AND status = 'COMPLETED'"
    )['revenue'];
    
    // Average savings per package
    $packageSavings = $database->fetchAll(
        "SELECT p.id, p.price,
                COALESCE(SUM(s.price), 0) as original_price
         FROM packages p
         LEFT JOIN package_services ps ON p.id = ps.package_id
         LEFT JOIN services s ON ps.service_id = s.id
         WHERE p.is_active = 1
         GROUP BY p.id, p.price"
    );
    
    $totalSavings = 0;
    $packageCount = count($packageSavings);
    
    foreach ($packageSavings as $package) {
        $savings = max(0, $package['original_price'] - $package['price']);
        $totalSavings += $savings;
    }
    
    $stats['avg_savings'] = $packageCount > 0 ? $totalSavings / $packageCount : 0;
    
    // Most popular packages
    $stats['popular_packages'] = $database->fetchAll(
        "SELECT p.name, COUNT(b.id) as booking_count,
                COALESCE(SUM(CASE WHEN b.status = 'COMPLETED' THEN b.total_amount ELSE 0 END), 0) as revenue
         FROM packages p
         LEFT JOIN bookings b ON p.id = b.package_id
         GROUP BY p.id, p.name
         ORDER BY booking_count DESC
         LIMIT 5"
    );
    
    return $stats;
}

/**
 * Calculate package savings
 */
function calculatePackageSavings($packageId) {
    global $database;
    
    try {
        $result = $database->fetch(
            "SELECT p.price as package_price,
                    COALESCE(SUM(s.price), 0) as original_price
             FROM packages p
             LEFT JOIN package_services ps ON p.id = ps.package_id
             LEFT JOIN services s ON ps.service_id = s.id
             WHERE p.id = ?
             GROUP BY p.id, p.price",
            [$packageId]
        );
        
        if ($result) {
            $savings = max(0, $result['original_price'] - $result['package_price']);
            $discountPercent = $result['original_price'] > 0 
                ? round(($savings / $result['original_price']) * 100, 2)
                : 0;
            
            return [
                'original_price' => $result['original_price'],
                'package_price' => $result['package_price'],
                'savings' => $savings,
                'discount_percent' => $discountPercent
            ];
        }
        
        return null;
        
    } catch (Exception $e) {
        error_log("Package savings calculation error: " . $e->getMessage());
        return null;
    }
}

/**
 * Get packages with their services and pricing details
 */
function getPackagesWithDetails() {
    global $database;
    
    try {
        $packages = $database->fetchAll(
            "SELECT * FROM packages WHERE is_active = 1 ORDER BY name"
        );
        
        foreach ($packages as $index => $package) {
            $packages[$index]['services'] = getPackageServices($package['id']);
            $packages[$index]['savings_info'] = calculatePackageSavings($package['id']);
        }
        
        return $packages;
        
    } catch (Exception $e) {
        error_log("Get packages with details error: " . $e->getMessage());
        return [];
    }
}

/**
 * Check if services can form a valid package
 */
function validatePackageServices($serviceIds, $packagePrice) {
    global $database;
    
    try {
        if (!is_array($serviceIds) || count($serviceIds) < 2) {
            return ['valid' => false, 'error' => 'Package must include at least 2 services'];
        }
        
        // Get total price of individual services
        $placeholders = str_repeat('?,', count($serviceIds) - 1) . '?';
        $services = $database->fetchAll(
            "SELECT id, name, price FROM services WHERE id IN ($placeholders) AND is_active = 1",
            $serviceIds
        );
        
        if (count($services) !== count($serviceIds)) {
            return ['valid' => false, 'error' => 'One or more selected services are invalid or inactive'];
        }
        
        $totalOriginalPrice = array_sum(array_column($services, 'price'));
        
        if ($packagePrice >= $totalOriginalPrice) {
            return ['valid' => false, 'error' => 'Package price must be less than individual service prices'];
        }
        
        $savings = $totalOriginalPrice - $packagePrice;
        $discountPercent = ($savings / $totalOriginalPrice) * 100;
        
        return [
            'valid' => true,
            'original_price' => $totalOriginalPrice,
            'package_price' => $packagePrice,
            'savings' => $savings,
            'discount_percent' => round($discountPercent, 2),
            'services' => $services
        ];
        
    } catch (Exception $e) {
        error_log("Package validation error: " . $e->getMessage());
        return ['valid' => false, 'error' => 'Validation failed'];
    }
}

/**
 * Get package performance metrics
 */
function getPackagePerformance($packageId, $startDate = null, $endDate = null) {
    global $database;
    
    try {
        if (!$startDate) {
            $startDate = date('Y-m-01'); // First day of current month
        }
        if (!$endDate) {
            $endDate = date('Y-m-t'); // Last day of current month
        }
        
        $performance = $database->fetch(
            "SELECT 
                COUNT(*) as total_bookings,
                COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) as completed_bookings,
                COUNT(CASE WHEN status = 'CANCELLED' THEN 1 END) as cancelled_bookings,
                COALESCE(SUM(CASE WHEN status = 'COMPLETED' THEN total_amount ELSE 0 END), 0) as total_revenue,
                COALESCE(AVG(CASE WHEN status = 'COMPLETED' THEN total_amount ELSE NULL END), 0) as avg_booking_value
             FROM bookings 
             WHERE package_id = ? AND date BETWEEN ? AND ?",
            [$packageId, $startDate, $endDate]
        );
        
        // Calculate conversion rate
        $performance['conversion_rate'] = $performance['total_bookings'] > 0 
            ? round(($performance['completed_bookings'] / $performance['total_bookings']) * 100, 2)
            : 0;
        
        return $performance;
        
    } catch (Exception $e) {
        error_log("Package performance error: " . $e->getMessage());
        return null;
    }
}
?>
