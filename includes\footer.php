    </main>

    <!-- Footer -->
    <?php $basePath = getBasePath(); ?>
    <footer class="bg-salon-black border-t border-secondary-700">
        <div class="mx-auto max-w-7xl px-6 py-16 sm:py-24 lg:px-8">
            <div class="xl:grid xl:grid-cols-3 xl:gap-8">
                <!-- Company info -->
                <div class="space-y-8">
                    <div>
                        <h3 class="text-2xl font-bold font-serif text-salon-gold">
                            Flix Salon & SPA
                        </h3>
                        <p class="mt-4 text-sm leading-6 text-gray-300">
                            Experience luxury and elegance at Flix Salon & SPA. We provide premium beauty services
                            with a touch of sophistication that makes every visit memorable.
                        </p>
                    </div>
                    
                    <!-- Contact info -->
                    <div class="space-y-4">
                        <div class="flex items-center space-x-3">
                            <svg class="h-5 w-5 text-salon-gold" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M15 10.5a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1115 0z" />
                            </svg>
                            <span class="text-sm text-gray-300">
                                Upanga, Dar Es Salaam, Tanzania
                            </span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <svg class="h-5 w-5 text-salon-gold" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 002.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 01-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 00-1.091-.852H4.5A2.25 2.25 0 002.25 4.5v2.25z" />
                            </svg>
                            <a href="tel:+255745456789" class="text-sm text-gray-300 hover:text-salon-gold transition-colors">
                                (255) 745 456-789
                            </a>
                        </div>
                        <div class="flex items-center space-x-3">
                            <svg class="h-5 w-5 text-salon-gold" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75" />
                            </svg>
                            <a href="mailto:<EMAIL>" class="text-sm text-gray-300 hover:text-salon-gold transition-colors">
                                <EMAIL>
                            </a>
                        </div>
                        <div class="flex items-center space-x-3">
                            <svg class="h-5 w-5 text-salon-gold" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span class="text-sm text-gray-300">
                                Mon-Sat: 9AM-7PM, Sun: 10AM-6PM
                            </span>
                        </div>
                    </div>

                    <!-- Social links -->
                    <div class="flex space-x-6">
                        <a href="#" class="text-gray-400 hover:text-salon-gold transition-colors">
                            <span class="sr-only">Instagram</span>
                            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                <path fill-rule="evenodd" d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987s11.987-5.367 11.987-11.987C24.004 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.418-3.323c.875-.875 2.026-1.297 3.323-1.297s2.448.422 3.323 1.297c.928.875 1.418 2.026 1.418 3.323s-.49 2.448-1.418 3.244c-.875.807-2.026 1.297-3.323 1.297zm7.83-9.781c-.49 0-.928-.422-.928-.928 0-.49.438-.928.928-.928.49 0 .928.438.928.928 0 .506-.438.928-.928.928zM12.017 7.356c-2.448 0-4.474 2.026-4.474 4.474s2.026 4.474 4.474 4.474 4.474-2.026 4.474-4.474-2.026-4.474-4.474-4.474z" clip-rule="evenodd" />
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-salon-gold transition-colors">
                            <span class="sr-only">Facebook</span>
                            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                <path fill-rule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clip-rule="evenodd" />
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-salon-gold transition-colors">
                            <span class="sr-only">Twitter</span>
                            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Navigation links -->
                <div class="mt-16 grid grid-cols-2 gap-8 xl:col-span-2 xl:mt-0">
                    <div class="md:grid md:grid-cols-2 md:gap-8">
                        <div>
                            <h3 class="text-sm font-semibold leading-6 text-white">Services</h3>
                            <ul role="list" class="mt-6 space-y-4">
                                <li>
                                    <a href="<?= $basePath ?>/services#hair" class="text-sm leading-6 text-gray-300 hover:text-salon-gold transition-colors">Hair Styling</a>
                                </li>
                                <li>
                                    <a href="<?= $basePath ?>/services#coloring" class="text-sm leading-6 text-gray-300 hover:text-salon-gold transition-colors">Hair Coloring</a>
                                </li>
                                <li>
                                    <a href="<?= $basePath ?>/services#facial" class="text-sm leading-6 text-gray-300 hover:text-salon-gold transition-colors">Facial Treatments</a>
                                </li>
                                <li>
                                    <a href="<?= $basePath ?>/services#nails" class="text-sm leading-6 text-gray-300 hover:text-salon-gold transition-colors">Manicure & Pedicure</a>
                                </li>
                                <li>
                                    <a href="<?= $basePath ?>/services#massage" class="text-sm leading-6 text-gray-300 hover:text-salon-gold transition-colors">Massage Therapy</a>
                                </li>
                            </ul>
                        </div>
                        <div class="mt-10 md:mt-0">
                            <h3 class="text-sm font-semibold leading-6 text-white">Company</h3>
                            <ul role="list" class="mt-6 space-y-4">
                                <li>
                                    <a href="<?= $basePath ?>/about" class="text-sm leading-6 text-gray-300 hover:text-salon-gold transition-colors">About Us</a>
                                </li>
                                <li>
                                    <a href="<?= $basePath ?>/our-team" class="text-sm leading-6 text-gray-300 hover:text-salon-gold transition-colors">Our Team</a>
                                </li>
                                <li>
                                    <a href="<?= $basePath ?>/careers" class="text-sm leading-6 text-gray-300 hover:text-salon-gold transition-colors">Careers</a>
                                </li>
                                <li>
                                    <a href="<?= $basePath ?>/contact" class="text-sm leading-6 text-gray-300 hover:text-salon-gold transition-colors">Contact</a>
                                </li>
                                <li>
                                    <a href="<?= $basePath ?>/blog" class="text-sm leading-6 text-gray-300 hover:text-salon-gold transition-colors">Blog</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="md:grid md:grid-cols-2 md:gap-8">
                        <div>
                            <h3 class="text-sm font-semibold leading-6 text-white">Support</h3>
                            <ul role="list" class="mt-6 space-y-4">
                                <li>
                                    <a href="<?= $basePath ?>/booking-policy" class="text-sm leading-6 text-gray-300 hover:text-salon-gold transition-colors">Booking Policy</a>
                                </li>
                                <li>
                                    <a href="<?= $basePath ?>/cancellation-policy" class="text-sm leading-6 text-gray-300 hover:text-salon-gold transition-colors">Cancellation</a>
                                </li>
                                <li>
                                    <a href="<?= $basePath ?>/gift-cards" class="text-sm leading-6 text-gray-300 hover:text-salon-gold transition-colors">Gift Cards</a>
                                </li>
                                <li>
                                    <a href="<?= $basePath ?>/faq" class="text-sm leading-6 text-gray-300 hover:text-salon-gold transition-colors">FAQ</a>
                                </li>
                                <li>
                                    <a href="<?= $basePath ?>/reviews" class="text-sm leading-6 text-gray-300 hover:text-salon-gold transition-colors">Reviews</a>
                                </li>
                            </ul>
                        </div>
                        <div class="mt-10 md:mt-0">
                            <h3 class="text-sm font-semibold leading-6 text-white">Newsletter</h3>
                            <p class="mt-2 text-sm leading-6 text-gray-300">
                                Subscribe to our newsletter for the latest updates and exclusive offers.
                            </p>
                            <form class="mt-6 sm:flex sm:max-w-md" action="<?= $basePath ?>/api/newsletter.php" method="POST">
                                <label for="email-address" class="sr-only">Email address</label>
                                <input type="email" name="email" id="email-address" autocomplete="email" required 
                                       class="w-full min-w-0 appearance-none rounded-md border-0 bg-white/5 px-3 py-1.5 text-base text-white shadow-sm ring-1 ring-inset ring-white/10 placeholder:text-gray-500 focus:ring-2 focus:ring-inset focus:ring-salon-gold sm:w-64 sm:text-sm sm:leading-6 xl:w-full" 
                                       placeholder="Enter your email">
                                <div class="mt-4 sm:ml-4 sm:mt-0 sm:flex-shrink-0">
                                    <button type="submit" 
                                            class="flex w-full items-center justify-center rounded-md bg-salon-gold px-3 py-2 text-sm font-semibold text-black shadow-sm hover:bg-gold-light focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-salon-gold transition-colors">
                                        Subscribe
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Bottom section -->
            <div class="mt-16 border-t border-white/10 pt-8 sm:mt-20 lg:mt-24">
                <div class="flex flex-col items-center justify-between gap-4 sm:flex-row">
                    <p class="text-xs leading-5 text-gray-400">
                        &copy; <?= date('Y') ?> Flix Salon & SPA. All rights reserved. | Developed By Digitize Creates
                    </p>
                    <div class="flex gap-x-6">
                        <a href="<?= $basePath ?>/privacy" class="text-xs leading-5 text-gray-400 hover:text-salon-gold transition-colors">Privacy Policy</a>
                        <a href="<?= $basePath ?>/terms" class="text-xs leading-5 text-gray-400 hover:text-salon-gold transition-colors">Terms of Service</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Include Mobile Menu Component -->
    <?php include __DIR__ . '/mobile-menu.php'; ?>

    <!-- Newsletter form submission -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Newsletter form submission - handle all newsletter forms
            const newsletterForms = document.querySelectorAll('form[action*="/api/newsletter.php"], form[action*="newsletter"]');

            newsletterForms.forEach(function(form) {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const formData = new FormData(this);
                    const email = formData.get('email');
                    const submitButton = this.querySelector('button[type="submit"]');
                    const originalText = submitButton.textContent;

                    // Validate email
                    if (!email || !email.includes('@')) {
                        showNotification('Please enter a valid email address.', 'error');
                        return;
                    }

                    // Show loading state
                    submitButton.textContent = 'Subscribing...';
                    submitButton.disabled = true;

                    fetch('<?= $basePath ?>/api/newsletter.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ email: email })
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success) {
                            showNotification('Thank you for subscribing to our newsletter!', 'success');
                            this.reset();
                        } else {
                            showNotification(data.error || 'Failed to subscribe. Please try again.', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Newsletter subscription error:', error);
                        showNotification('Failed to subscribe. Please check your connection and try again.', 'error');
                    })
                    .finally(() => {
                        // Reset button state
                        submitButton.textContent = originalText;
                        submitButton.disabled = false;
                    });
                });
            });

            // Notification function
            function showNotification(message, type = 'info') {
                // Remove existing notifications
                const existingNotifications = document.querySelectorAll('.newsletter-notification');
                existingNotifications.forEach(n => n.remove());

                // Create notification
                const notification = document.createElement('div');
                notification.className = `newsletter-notification fixed top-4 right-4 z-50 max-w-sm p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;

                if (type === 'success') {
                    notification.className += ' bg-green-600 text-white';
                } else if (type === 'error') {
                    notification.className += ' bg-red-600 text-white';
                } else {
                    notification.className += ' bg-blue-600 text-white';
                }

                notification.innerHTML = `
                    <div class="flex items-center">
                        <div class="flex-1">
                            <p class="text-sm font-medium">${message}</p>
                        </div>
                        <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                `;

                document.body.appendChild(notification);

                // Animate in
                setTimeout(() => {
                    notification.classList.remove('translate-x-full');
                }, 100);

                // Auto remove after 5 seconds
                setTimeout(() => {
                    notification.classList.add('translate-x-full');
                    setTimeout(() => notification.remove(), 300);
                }, 5000);
            }
        });
    </script>
</body>
</html>
